{"permissions": {"allow": ["Bash(rm:*)", "Bash(ls:*)", "Bash(git checkout:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(clear)", "Bash(find:*)", "Bash(npm install:*)", "Bash(npm uninstall:*)", "<PERSON><PERSON>(test:*)", "<PERSON><PERSON>(mkdir:*)", "WebFetch(domain:mini.zalo.me)", "WebFetch(domain:miniapp.zaloplatforms.com)", "Bash(npx tsc:*)", "Bash(npx typescript:*)", "Bash(npm ls:*)", "WebFetch(domain:developers.google.com)", "Bash(npm run lint)", "Bash(npm run:*)"], "deny": []}}