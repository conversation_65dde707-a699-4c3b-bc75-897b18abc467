# UTM Parameter Integration Complete

## Summary

UTM parameter tracking has been successfully integrated into the GA4 tracking system. All GA4 events now automatically include UTM campaign attribution data when users arrive via marketing campaigns.

## Implementation Details

### 1. **Imports Added** (Line 4)
```typescript
import { initUTMTracking, getCurrentUTMParameters, formatUTMForGA4 } from './utm-tracking';
```

### 2. **Enhanced sendGA4Event Function** (Lines 207-218)
- Automatically retrieves current UTM parameters using `getCurrentUTMParameters()`
- Formats UTM parameters for GA4 using `formatUTMForGA4()`
- Merges formatted UTM parameters with event parameters
- UTM parameters are mapped to GA4's campaign fields:
  - `utm_source` → `campaign_source`
  - `utm_medium` → `campaign_medium`
  - `utm_campaign` → `campaign_name`
  - `utm_term` → `campaign_term`
  - `utm_content` → `campaign_content`

### 3. **UTM Initialization in initGA4** (Lines 564-586)
- Calls `initUTMTracking()` when GA4 initializes
- Captures UTM parameters from URL on app load
- Logs UTM parameters in console (including development mode)
- Tracks a `utm_parameters_captured` event when UTM params are present

### 4. **Development Logging Enhanced** (Lines 267-273)
- GA4 events now include `utm_parameters` in console logs
- Helps developers verify UTM tracking is working correctly

### 5. **New UTM Tracking Event** (Lines 455-473)
- Added `trackUTMParametersCaptured()` function
- Sends a specific event when UTM parameters are detected
- Includes all UTM parameters and capture location

## How It Works

1. **On App Load**:
   - `initGA4()` is called
   - `initUTMTracking()` extracts UTM parameters from URL
   - Parameters are stored in localStorage (24-hour expiry)
   - If UTM params exist, `utm_parameters_captured` event is sent

2. **For Every Event**:
   - `sendGA4Event()` automatically includes current UTM parameters
   - Parameters are formatted for GA4 (campaign_source, etc.)
   - No need to manually add UTM params to individual events

3. **Persistence**:
   - UTM parameters survive page navigation
   - Stored for 24 hours in localStorage
   - Automatically included in all events during session

## Testing

### 1. Test with UTM Parameters
```
https://yourapp.com/?utm_source=facebook&utm_medium=social&utm_campaign=winter2024
```

### 2. Check Console (Development Mode)
```javascript
// You'll see logs like:
GA4 Event: {
  name: "game_start",
  params: {
    game_session_id: "abc-123",
    timestamp: "2024-01-15T10:30:00.000Z",
    campaign_source: "facebook",
    campaign_medium: "social",
    campaign_name: "winter2024",
    // ... other params
  },
  utm_parameters: {
    utm_source: "facebook",
    utm_medium: "social",
    utm_campaign: "winter2024"
  }
}
```

### 3. Verify in GA4
- Check Realtime → Events
- Look for `utm_parameters_captured` event on app load
- Verify all events include campaign parameters

## Benefits

1. **Automatic Attribution**: All events include campaign data without manual coding
2. **Consistency**: UTM parameters are consistently formatted for GA4
3. **Persistence**: Campaign attribution survives navigation
4. **Debugging**: Clear logging in development mode
5. **Zero Maintenance**: Works automatically for all existing and future events

## Zalo Mini Program Support

The implementation handles both:
- Regular web browsers (URL parameters)
- Zalo Mini Program (via `zalo.getQueryParams()` and URL parsing)
- Hash parameters (for SPAs and deep links)

## Event Examples

### Page View Event
```json
{
  "name": "page_view",
  "params": {
    "page_title": "WelcomeScreen",
    "campaign_source": "zalo",
    "campaign_medium": "message",
    "campaign_name": "friend_share"
  }
}
```

### Game Start Event
```json
{
  "name": "game_start",
  "params": {
    "game_session_id": "uuid-here",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "campaign_source": "facebook",
    "campaign_medium": "paid_social",
    "campaign_name": "winter_campaign"
  }
}
```

All events now automatically include UTM campaign parameters when available!