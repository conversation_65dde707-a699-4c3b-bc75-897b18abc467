import React, { useState, useRef } from 'react';
import { But<PERSON>, Box, Text, Page, Modal, useSnackbar } from 'zmp-ui';
import { useNavigate } from 'zmp-ui';
import GameHeader from './GameHeader';
import heroImage from '@/static/welcome/hero.png';
import flowerImage from '@/static/flower.png';
import ensureLogoImage from '@/static/welcome/ensure-logo.png';
import decorImage from '@/static/welcome/decor.png';
import selectButtonImage from '@/static/select-button.png';
import logoBrandImage from '@/static/logo-brand.png';
import { trackScreenView, trackGameStart, sendGA4Event } from '@/utils/ga4-tracking';
import prizesImage from '@/static/prize/prizes.png'
import { getSystemInfo } from "zmp-sdk";
import { storageData } from '@/utils/api';
import { extractUTMParameters } from '@/utils/utm-tracking';
interface WelcomeScreenProps {
  onStart: () => void;
  sessionId: string;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ onStart, sessionId }) => {
  const navigate = useNavigate();
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [showUpdateDialog, setShowUpdateDialog] = useState(false);
  const [zaloVersionValid, setZaloVersionValid] = useState(true);
  const { openSnackbar } = useSnackbar();

  React.useEffect(() => {
    // Track screen view when component mounts
    trackScreenView('WelcomeScreen', 'Game');

    // Extract and log URL parameters
    const urlParams = extractUTMParameters();
    console.log('URL Parameters:', urlParams);

    // Send UTM parameters to storage API if available
    if (Object.keys(urlParams).length > 0) {
      const utmString = JSON.stringify(urlParams);
      console.log('UTM String:', utmString);

      // Call storageData with UTM parameters
      storageData({
        id: sessionId,
        utm: utmString
      }).catch((error) => {
        console.error('Error storing UTM data:', error);
      });
    }

    // Get and store system info in localStorage
    const systemInfo = getSystemInfo();
    console.log(systemInfo);

    localStorage.setItem("systemInfo", JSON.stringify(systemInfo));
    if (systemInfo) {
      try {
        const platform = systemInfo.platform?.toLowerCase();
        const zaloVersion = systemInfo.zaloVersion || '';

        // Check minimum Zalo version
        let isValidVersion = true;
        if (platform === 'android') {
          // Android: zaloVersion >= "23.12.02 (724)"
          isValidVersion = compareZaloVersion(zaloVersion, '23120200');
        } else if (platform === 'ios') {
          // iOS: zaloVersion >= "23.12.02 (573)"
          isValidVersion = compareZaloVersion(zaloVersion, '573');
        }

        setZaloVersionValid(isValidVersion);
        if (!isValidVersion) {
          setShowUpdateDialog(true);
        }
      } catch (error) {
        console.error('Error parsing system info:', error);
      }
    }
  }, [sessionId]);

  // Helper function to compare Zalo versions
  const compareZaloVersion = (currentVersion: string, minVersion: string): boolean => {
    try {
      // Chuyển về số nguyên
      const currentNum = parseInt(currentVersion, 10);
      const minNum = parseInt(minVersion, 10);

      if (isNaN(currentNum) || isNaN(minNum)) {
        console.error("Invalid version format");
        return false;
      }

      // So sánh
      return currentNum >= minNum;
    } catch (error) {
      console.error("Error comparing Zalo versions:", error);
      return false;
    }
  };

  const handleStartGame = async () => {

    // Check if Zalo version is valid
    if (!zaloVersionValid) {
      setShowUpdateDialog(true);
      return;
    }

    // Track game start clicked event using the provided sessionId
    await sendGA4Event({
      name: 'game_start_clicked',
      params: {
        screen_name: 'WelcomeScreen',
        button_location: 'footer',
        button_text: 'THAM GIA NGAY'
      }
    });

    // Track game start with additional parameters
    await trackGameStart(sessionId, {
      screen_name: 'WelcomeScreen'
    });

    onStart();
    navigate('/upload');
  };



  return (
    <Page
      className="welcome-screen"
    >
      {/* Header */}
      <GameHeader />

      {/* Main Content Area */}
      <Box className="main-content !p-0" style={{ position: 'relative' }}>
        {/* Top positioned images */}
        <img
          src={ensureLogoImage}
          alt="Ensure Logo"
          style={{ position: 'absolute', top: 0, left: 0 }}
          className="ensure-logo"
        />
        <img
          src={decorImage}
          alt="Decor"
          style={{ position: 'absolute', top: 0, right: 0 }}
          className="decor-image"
        />


        {/* Hero Section */}
        <Box className="hero-section !p-[24px] !m-0">
          <h1 className="primary-heading gradient-text">Bé Ngoan</h1>
          <p className="secondary-text gradient-text">lớn rồi có còn nhận được hoa hồng?</p>

          <img src={heroImage} alt="Hero" className="hero-image " />

          <p className="intro-text gradient-text">
            Cùng Ensure Gold tạo phiếu Bé Ngoan <br /> và thu thập hoa hồng về khoe Cha Mẹ
          </p>



          {/* Action Buttons */}
          <Box className="action-buttons !pt-2">
            <button
              className="image-button enhanced-button"
              onClick={handleStartGame}
            >
              <img src={selectButtonImage} alt="Start Game" />
              <span className="button-text gradient-text">THAM GIA NGAY</span>

            </button>
          </Box>

          {/* Terms and Conditions Text */}
          <Box className="terms-checkbox-container">
            <span className="terms-text">
              Bằng việc nhấn tham gia ngay <br /> bạn đã đồng ý các{' '}
              <span
                className="terms-link"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowTermsModal(true);
                }}
              >
                thể lệ và điều khoản của chương trình
              </span>
            </span>
          </Box>
          <p className="cta-primary gradient-text">Cơ hội nhận quà hấp dẫn mỗi tuần</p>
          <p className="cta-secondary gradient-text">khi tham gia tạo phiếu Bé Ngoan</p>

          {/* <PrizesSection className="pt-3" /> */}

        </Box>
        <img
          src={prizesImage}
          className='w-full z-9'
          alt="Prize"
        />
      </Box>

      {/* Selected Zone (Footer) */}
      <Box
        className="selected-zone selected-zone-welcome"
      >
        {/* Brand Logo */}
        <img
          src={logoBrandImage}
          alt="Brand Logo"
          style={{
            position: 'absolute',
            bottom: '18px',
            right: '14px',
            width: 'auto',
            height: '16px',
            zIndex: 10
          }}
        />

        {/* QC */}
        <div
          style={{
            fontSize: '6px',
            color: 'white',
            position: 'absolute',
            bottom: '14px',
            left: '14px',
            width: 'auto',
            height: '20px',
            zIndex: 10
          }}>
          ENS-C-630-25
        </div>
      </Box>

      {/* Terms and Conditions Modal */}
      <Modal
        visible={showTermsModal}
        onClose={() => setShowTermsModal(false)}
        className="terms-modal"
      >

        <Box className="terms-dialog">
          <button
            className="close-button"
            onClick={() => setShowTermsModal(false)}
            aria-label="Đóng"
            role="button"
          >
            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M13 1L1.00081 12.9992M12.9992 13L1 1.00085" stroke="url(#paint0_linear_395_3690)" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              <defs>
                <linearGradient id="paint0_linear_395_3690" x1="7" y1="1" x2="7" y2="13" gradientUnits="userSpaceOnUse">
                  <stop stopColor="#1A3A6F" />
                  <stop offset="0.5" stopColor="#2071B3" />
                  <stop offset="1" stopColor="#1A3A6F" />
                </linearGradient>
              </defs>
            </svg>
          </button>
          <Box className="terms-dialog-content">
            <Text className="terms-heading gradient-text-gold">Thể lệ và điều khoản chương trình</Text>

            <Box className="terms-content">
              <p>
                Chào mừng bạn đến với Mini App "Tạo phiếu bé ngoan tặng cha mẹ" cùng Ensure Gold.
                Vui lòng đọc kỹ các điều khoản sử dụng này trước khi sử dụng ứng dụng.
              </p>

              <p>
                <strong>1. Mô tả ứng dụng:</strong> "Tạo phiếu bé ngoan tặng cha mẹ" cùng Ensure Gold là trò chơi giải trí,
                cho phép người dùng đăng tải hình ảnh gia đình, trả lời một số câu hỏi trắc nghiệm vui
                để nhận được sổ phiếu bé ngoan và chia sẻ tới bố mẹ, những người thân yêu.
              </p>

              <p>
                <strong>2. Quyền truy cập và sử dụng dữ liệu:</strong> Để đảm bảo trải nghiệm người dùng tốt nhất,
                chúng tôi yêu cầu người dùng cấp các quyền sau đây: Truy cập camera: Quyền này giúp ứng dụng
                truy cập vào camera của thiết bị để người dùng có thể trực tiếp chụp ảnh và tải ảnh lên ứng dụng.
                Truy cập media (camera, ảnh, file, video): Quyền này cho phép người dùng lựa chọn và tải lên
                các file hình ảnh, video từ thư viện ảnh hoặc tệp tin của thiết bị để sử dụng trong ứng dụng.
                Truy cập số điện thoại: Quyền này cho phép ứng dụng xác thực và hỗ trợ người dùng tốt hơn.
                Chúng tôi sẽ yêu cầu bạn đồng ý rõ ràng trước khi thu thập thông tin này.
              </p>

              <p>
                <strong>3. Thu thập và xử lý dữ liệu cá nhân:</strong> Chúng tôi cam kết chỉ thu thập và xử lý dữ liệu
                trong phạm vi các quyền được nêu rõ trong điều khoản sử dụng này. Dữ liệu cá nhân mà chúng tôi
                thu thập bao gồm: Ảnh do người dùng tải lên. Thông tin số điện thoại (nếu người dùng đồng ý).
                Chúng tôi chỉ sử dụng những dữ liệu này để cung cấp dịch vụ và cải thiện trải nghiệm người dùng
                trong ứng dụng.
              </p>

              <p>
                <strong>4. Bảo mật dữ liệu:</strong> Chúng tôi cam kết bảo mật tuyệt đối dữ liệu cá nhân của người dùng,
                không chia sẻ với bên thứ ba khi chưa có sự đồng ý của người dùng, trừ khi pháp luật có quy định khác.
              </p>

              <p>
                <strong>5. Trách nhiệm người dùng:</strong> Người dùng cam kết cung cấp thông tin cá nhân đúng, chính xác
                và chịu trách nhiệm về các hình ảnh và dữ liệu mà mình tải lên.
              </p>

              <p>
                <strong>6. Thay đổi và cập nhật:</strong> Chúng tôi có quyền thay đổi nội dung của các điều khoản sử dụng
                này bất cứ lúc nào. Mọi cập nhật sẽ được thông báo trong ứng dụng hoặc trên trang thông tin
                chính thức của chúng tôi. Việc tiếp tục sử dụng ứng dụng sau khi các điều khoản được cập nhật
                đồng nghĩa với việc bạn đồng ý với những thay đổi này.
              </p>

              <p>
                Bằng việc tiếp tục sử dụng ứng dụng "Tạo phiếu bé ngoan tặng cha mẹ" cùng Ensure Gold,
                bạn xác nhận đã đọc, hiểu và đồng ý với tất cả các điều khoản nêu trên.
              </p>
            </Box>
          </Box>
        </Box>
      </Modal>

      {/* Update Zalo Dialog Modal */}
      <Modal
        visible={showUpdateDialog}
        onClose={() => setShowUpdateDialog(false)}
        className="update-dialog-modal"
      >
        <Box className="update-dialog">
          <Box className="update-dialog-content">
            <Text className="update-heading gradient-text-gold">Cập nhật Zalo</Text>

            <Box className="update-message-section">
              <Text className="update-icon" style={{ fontSize: '48px', textAlign: 'center', marginBottom: '16px' }}>⚠️</Text>
              <Text className="update-message" style={{ textAlign: 'center', marginBottom: '8px' }}>
                Phiên bản Zalo của bạn cần được cập nhật để chơi game này.
              </Text>
              <Text className="update-subtitle" style={{ textAlign: 'center', fontSize: '14px', opacity: 0.8 }}>
                Vui lòng cập nhật lên phiên bản mới nhất của Zalo để tiếp tục.
              </Text>
            </Box>

            <Box className="update-actions" style={{ marginTop: '24px', display: 'flex', justifyContent: 'center' }}>
              <button
                className="update-button"
                onClick={() => setShowUpdateDialog(false)}
                style={{
                  padding: '12px 24px',
                  background: 'linear-gradient(180deg, #FFE57F 0%, #FFC107 100%)',
                  border: 'none',
                  borderRadius: '8px',
                  color: '#1A3A6F',
                  fontWeight: 'bold',
                  cursor: 'pointer'
                }}
              >
                Đã hiểu
              </button>
            </Box>
          </Box>
        </Box>
      </Modal>
    </Page>
  );
};

export default WelcomeScreen;
