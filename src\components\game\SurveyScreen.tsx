import React, { useState, useEffect } from 'react';
import { Box, Page, useNavigate, useSnackbar } from 'zmp-ui';
import { authorize, AppError, getPhoneNumber, getAccessToken, getUserInfo } from 'zmp-sdk';
import { DotLottieReact } from '@lottiefiles/dotlottie-react';
import { v4 as uuidv4 } from 'uuid';
import GameHeader from './GameHeader';
import selectButtonImage from '@/static/select-button.png';
import surveyBgImage from '@/static/survey/bg-survey.png';
import numberBgImage from '@/static/survey/number-q-bg.png';
import questionBgImage from '@/static/survey/question-bg.png';
import flowerImage from '@/static/flower.png';
import noteBookImage from '@/static/survey/note-book.png';
import bgLoadingImage from '@/static/survey/bg-loading.png';
import logoBrandImage from '@/static/logo-brand.png';
import img0 from '@/static/survey/0.png';
import img1 from '@/static/survey/1.png';
import img2 from '@/static/survey/2.png';
import img3 from '@/static/survey/3.png';
import img10 from '@/static/survey/10.png';
import img11 from '@/static/survey/11.png';
import img12 from '@/static/survey/12.png';
import flowerImageDecor from '@/static/survey/flower-decor.png';
import flowerImageDecor2 from '@/static/survey/flower-decor-2.png';
import flowerLoadingGif from '@/static/survey/flower-loading.gif';
import { trackScreenView, trackSurveyResponse, trackSurveyComplete, trackError, sendGA4Event } from '@/utils/ga4-tracking';
import { generateVideo, storageData } from '@/utils/api';

interface Question {
  id: string;
  type: 'checkbox';
  question: string;
  options: string[];
  required: boolean;
  allowMultiple?: boolean;
}

interface SurveyResponse {
  questionId: string;
  answer: string | string[] | number;
}

interface SurveyScreenProps {
  onSurveyComplete: (responses: SurveyResponse[], userData?: any) => void;
  uploadedImages: File[];
}

// Custom Checkbox Component
interface CustomCheckboxProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  label: string;
}

const CustomCheckbox: React.FC<CustomCheckboxProps> = ({ checked, onChange, label }) => {
  return (
    <Box
      className="custom-checkbox-container"
      onClick={() => onChange(!checked)}
    >
      <Box className="custom-checkbox">
        {checked && (
          <svg viewBox="0 0 25 15" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6.93903 14.1353C11.2284 9.81297 13.1179 6.10297 24.0403 1.21877C24.246 1.12677 24.2863 0.846883 24.0961 0.726034C19.0149 -2.50197 9.54064 5.84049 6.07178 9.9298C6.03248 9.97613 5.96371 9.97451 5.92307 9.92935L1.98442 5.55308C1.69824 5.2351 1.23495 5.14736 0.852311 5.33867L0.790526 5.36957C0.604505 5.46258 0.463052 5.62567 0.397284 5.82298C0.146177 6.5763 0.251118 7.40292 0.682497 8.0696L4.41695 13.841C4.98423 14.7177 6.20348 14.8765 6.93903 14.1353Z" fill="#0C1BAC" />
          </svg>
        )}
      </Box>
      <span className="custom-checkbox-label">{label}</span>
    </Box>
  );
};

const SurveyScreen: React.FC<SurveyScreenProps> = ({ onSurveyComplete, uploadedImages }) => {
  const navigate = useNavigate();
  const [responses, setResponses] = useState<SurveyResponse[]>([]);
  const [userInfo, setUserInfoState] = useState({
    name: "",
    avatar: "",
    idByOA: null as string | null,
    mobile: null as string | null
  });
  const { openSnackbar } = useSnackbar();

  const [showLoadingDialog, setShowLoadingDialog] = useState(false);
  const [surveyStartTime] = useState(Date.now());
  const [visibleImages, setVisibleImages] = useState<number[]>([]);
  const [typedTitle, setTypedTitle] = useState('');
  const [typedSubtitle, setTypedSubtitle] = useState('');

  // Calculate score based on total selected options
  const calculateScore = (): number => {
    let totalSelections = 0;

    responses.forEach(response => {
      if (Array.isArray(response.answer)) {
        totalSelections += response.answer.length;
      } else if (response.answer) {
        totalSelections += 1;
      }
    });

    // Score logic:
    // 26-30 selections = 3 points
    // 21-25 selections = 2 points
    // ≤20 selections = 1 point
    if (totalSelections >= 26 && totalSelections <= 30) {
      return 3;
    } else if (totalSelections >= 21 && totalSelections <= 25) {
      return 2;
    } else {
      return 1;
    }
  };

  useEffect(() => {
    // Track screen view when component mounts
    trackScreenView('SurveyScreen', 'Game');
  }, []);

  // Handle sequential image animation
  useEffect(() => {
    if (showLoadingDialog) {
      // Reset visible images when dialog opens
      setVisibleImages([]);

      // Image sequence: 10, 11, 12, 0, 1, 2, 3
      const imageSequence = [10, 11, 12, 0, 1, 2, 3];
      const timers: NodeJS.Timeout[] = [];

      imageSequence.forEach((imageNum, index) => {
        const timer = setTimeout(() => {
          setVisibleImages(prev => [...prev, imageNum]);
        }, index * 300); // 0.3 second delay between each image
        timers.push(timer);
      });

      // Cleanup timers on unmount or when dialog closes
      return () => {
        timers.forEach(timer => clearTimeout(timer));
      };
    }
  }, [showLoadingDialog]);

  // Typing animation for loading dialog
  useEffect(() => {
    if (showLoadingDialog) {
      // Reset typed text
      setTypedTitle('');
      setTypedSubtitle('');

      const titleText = 'Phiếu bé ngoan \nđang được chuẩn bị';
      const subtitleText = 'Vui lòng chờ trong 1 phút nhé';
      let titleIndex = 0;
      let subtitleIndex = 0;
      let subtitleInterval: NodeJS.Timeout;

      // Type title
      const titleInterval = setInterval(() => {
        if (titleIndex < titleText.length) {
          // Get character before incrementing index
          const char = titleText[titleIndex];
          setTypedTitle(prev => prev + char);
          titleIndex++;
        } else {
          clearInterval(titleInterval);

          // Type subtitle after title completes
          subtitleInterval = setInterval(() => {
            if (subtitleIndex < subtitleText.length) {
              // Get character before incrementing index
              const char = subtitleText[subtitleIndex];
              setTypedSubtitle(prev => prev + char);
              subtitleIndex++;
            } else {
              clearInterval(subtitleInterval);
            }
          }, 100);
        }
      }, 100);

      return () => {
        clearInterval(titleInterval);
        if (subtitleInterval) {
          clearInterval(subtitleInterval);
        }
      };
    } else {
      // Reset when dialog closes
      setTypedTitle('');
      setTypedSubtitle('');
    }
  }, [showLoadingDialog]);


  // Survey questions về chủ đề gia đình và mùa Vu Lan
  const questions: Question[] = [
    {
      id: 'q1',
      type: 'checkbox',
      question: 'Thời gian qua, mình đã “vượt” chính mình như thế nào??',
      options: [
        'Biến "chưa biết" thành một chứng chỉ/bằng cấp đáng nể',
        'Dọn đến nơi ở mới – hoặc biến nhà thành tổ ấm thật sự',
        'Hoàn thành công việc/ dự án mà mình từng nghĩ “chắc mình sẽ không làm được đâu”',
        'Học cách “lắng nghe cơ thể mình” và chăm sóc đúng cách',
        'Vừa chăm được mình, vừa lo chu toàn cho sức khỏe Cha Mẹ',
        'Học cách “sống khỏe cho cả hai thế hệ”'
      ],
      required: true,
      allowMultiple: true
    },
    {
      id: 'q2',
      type: 'checkbox',
      question: 'Trước những bất định trong cuộc sống, đâu là động lực khiến mình tiếp tục cố gắng?',
      options: [
        'Nhớ lại lý do mình bắt đầu',
        'Tự nhủ: "Rồi sẽ ổn thôi mà"',
        'Nghỉ ngơi để tiếp thêm năng lượng',
        'Nghĩ đến gia đình, Cha Mẹ – điểm tựa vững chắc cho mình',
        'Tìm kiếm sự sẻ chia từ bạn bè và những người xung quanh'
      ],
      required: true,
      allowMultiple: true
    },
    {
      id: 'q3',
      type: 'checkbox',
      question: 'Trên hành trình nỗ lực và cố gắng ấy, gia đình với mình là…?',
      options: [
        'Điểm tựa vững vàng tiếp thêm sức mạnh cho mình',
        'Cái ô che chở mình giữa nắng mưa cuộc đời',
        'Vòng tay rộng mở đón mình trở về trong mọi hoàn cảnh',
        'Lời động viên truyền động lực',
        'Tấm gương sáng truyền cảm hứng để cho mình thấy thế nào là sống tử tế và kiên cường'
      ],
      required: true,
      allowMultiple: true
    },
    {
      id: 'q4',
      type: 'checkbox',
      question: 'Thay vì nói “con thương Cha Mẹ”, mình thường thể hiện sự quan tâm đến Cha Mẹ bằng cách nào?',
      options: [
        'Hỏi thăm sức khỏe thường xuyên, không chỉ “lúc rảnh”',
        'Dẫn Cha Mẹ đi khám định kỳ, không đợi khi ốm mới đi',
        'Vào bếp tự tay nấu bữa cơm gia đình cho Cha Mẹ',
        'Lắng nghe và dành thời gian trò chuyện với Cha Mẹ',
        'Nhắc Cha Mẹ uống sữa mỗi ngày, như cách họ từng chăm mình khi bé',
        'Thực hiện một hành động yêu thương mà mình biết Cha Mẹ sẽ không nói ra, nhưng sẽ rất vui trong lòng'
      ],
      required: true,
      allowMultiple: true
    },
    {
      id: 'q5',
      type: 'checkbox',
      question: 'Từ mùa Vu Lan này, mình muốn cùng Cha Mẹ tạo nên những khoảnh khắc nào?',
      options: [
        'Cùng đi xem bộ phim Cha Mẹ yêu thích',
        'Cùng dự một đêm nhạc gợi nhớ “thanh xuân”',
        'Cùng kể chuyện cũ, nghe Cha Mẹ nói “ngày xưa…”',
        'Cùng nấu một bữa cơm đúng vị nhà',
        'Cùng hát lại bản karaoke “hơi sai tông mà vui”',
        'Cùng tập thể dục – mỗi người một động tác, nhưng cùng nhịp',
        'Cùng đi du lịch – có thể gần thôi, nhưng là cùng nhau',
        'Làm gì cũng được chỉ cần làm cùng Cha Mẹ'
      ],
      required: true,
      allowMultiple: true
    }
  ];

  const handleAnswerChange = (questionId: string, answer: string | string[] | number) => {
    const newResponse: SurveyResponse = {
      questionId,
      answer
    };

    const updatedResponses = responses.filter(r => r.questionId !== questionId);
    updatedResponses.push(newResponse);
    setResponses(updatedResponses);
  };

  const handleCheckboxChange = async (questionId: string, option: string, checked: boolean) => {
    const currentAnswer = getAnswerForQuestion(questionId);
    let newAnswer: string[] = [];

    if (Array.isArray(currentAnswer)) {
      newAnswer = [...currentAnswer];
    }

    if (checked) {
      if (!newAnswer.includes(option)) {
        newAnswer.push(option);
      }
    } else {
      newAnswer = newAnswer.filter(item => item !== option);
    }

    handleAnswerChange(questionId, newAnswer);

    // Track survey response
    const question = questions.find(q => q.id === questionId);
    if (question) {
      const sessionId = localStorage.getItem('gameState') ?
        JSON.parse(localStorage.getItem('gameState') || '{}').sessionId :
        'unknown';
      await trackSurveyResponse(sessionId, questionId, question.question, 'checkbox');
    }
  };


  const handleGetUserInfo = () => {
    return new Promise<{
      name: string;
      avatar: string;
      idByOA: string | null;
      mobile: string | null;
    }>((resolve, reject) => {
      getUserInfo({
        autoRequestPermission: false,
        success: (res) => {
          console.log("getUserInfo success:", res);
          const sessionId = localStorage.getItem('gameState') ?
            JSON.parse(localStorage.getItem('gameState') || '{}').sessionId :
            'unknown';
          void storageData({
            id: sessionId,
            user_name: res.userInfo.name || "",
            avatar_url: res.userInfo.avatar || "",
            id_by_oa: res.userInfo.idByOA || "",
          }).catch(() => {
          });
          const userData = {
            name: res.userInfo.name || "",
            avatar: res.userInfo.avatar || "",
            idByOA: res.userInfo.idByOA || null,
            mobile: null, // Giữ nguyên số điện thoại hiện tại
          };
          console.log("get user info :", userData);


          // Lưu trực tiếp vào context với userData mới
          const contextData = {
            name: userData.name || undefined,
            avatar: userData.avatar || undefined,
            idByOA: userData.idByOA,
            mobile: userData.mobile || undefined,
          };
          console.log("User info saved to context:", contextData);
          resolve(userData);
        },
        fail: (error) => {
          console.error("Failed to get user info:", error);

          if (error && (error as AppError).code === -1401) {
            console.log("Người dùng từ chối cung cấp tên và ảnh đại diện");
          }

          reject(error);
        }
      });
    });
  };

  const handleGetPhoneNumber = () => {

    return new Promise((resolve, reject) => {
      getPhoneNumber({
        success: async (data) => {
          const { token } = data;
          console.log("Phone token:", data);

          try {
            const accessToken = await getAccessToken();

            const response = await fetch('https://automation.lifesup.ai/webhook/c18e43a8-158e-47ce-97a0-15a75b2f0b63', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                code: token,
                access_token: accessToken,
                secret_key: '8889DPF3NQV7Q169PT8L'
              }),
            });

            const result = await response.json();
            console.log("API result:", result);

            if (result.error === 0) {
              const phoneNum = result.data.number;
              const sessionId = localStorage.getItem('gameState') ?
                JSON.parse(localStorage.getItem('gameState') || '{}').sessionId :
                'unknown';
              void storageData({
                id: sessionId,
                phone_number: phoneNum
              }).catch(() => {
              });
              setUserInfoState(prev => {
                const updatedUserInfo = {
                  ...prev,
                  mobile: phoneNum
                };

                console.log("Updated user info:", updatedUserInfo);
                resolve(phoneNum);
                return updatedUserInfo;
              });
            } else {
              console.error("Lỗi khi lấy số điện thoại:", result.message);
              reject(new Error(result.message));
            }
          } catch (error) {
            console.error("Lỗi khi gọi API server:", error);
            reject(error);
          }
        },
        fail: (error) => {
          console.error("Lỗi khi lấy số điện thoại từ Zalo:", error);
          if (error.code === -1402) {
            console.log("Người dùng từ chối cung cấp số điện thoại");
          }
          reject(error);
        }
      });
    });
  };


  const handleStartPhone = async () => {
    try {
      const data = await authorize({
        scopes: ["scope.userInfo", "scope.userPhonenumber"],
      });
      console.log("user info", data["scope.userInfo"]);
      console.log("user phone", data["scope.userPhonenumber"]);

      // Sau khi cấp quyền, kiểm tra lại settings
      if (data["scope.userInfo"] && data["scope.userPhonenumber"]) {
        // Thực hiện cả 2 hàm song song
        const [userInfoResult] = await Promise.all([
          handleGetUserInfo(),
          handleGetPhoneNumber()
        ]);

        // Sau khi cả 2 hàm hoàn thành, hiển thị loading dialog
        setShowLoadingDialog(true);

        try {
          // Get user ID from localStorage (stored during image upload)
          // Get user ID from localStorage (stored during image upload)
          const gameState = localStorage.getItem('gameState');
          console.log(gameState);

          const userId = gameState ? JSON.parse(gameState).sessionId : uuidv4();
          console.log(userId);

          if (!userId) {
            throw new Error('User ID not found. Please restart the game.');
          }
          // Track survey completion
          const sessionId = localStorage.getItem('gameState') ?
            JSON.parse(localStorage.getItem('gameState') || '{}').sessionId :
            'unknown';
          // Calculate score based on survey responses
          const score = calculateScore();

          const surveyResponseText = responses
            .map(r => (Array.isArray(r.answer) ? r.answer : [r.answer]))
            .reduce((acc: string[], arr) => {
              for (const s of arr) {
                const t = String(s).trim();
                if (t) acc.push(`"${t.replace(/"/g, '\\"')}"`);
              }
              return acc;
            }, [])
            .join(', ');

          void storageData({
            id: sessionId,
            survey_score: score,
            survey_response: surveyResponseText
          }).catch(() => {
          });

          // Call the new video generation API
          const apiResult = await generateVideo(userId, score);

          if (apiResult.status === 'success' && apiResult.url) {
            // Close loading dialog
            setShowLoadingDialog(false);
            const completionTime = Math.floor((Date.now() - surveyStartTime) / 1000);
            await trackSurveyComplete(sessionId, questions.length, completionTime);

            // Navigate to results with API response data including video URL and mp4 URL
            onSurveyComplete(responses, {
              ...userInfoResult,
              videoUrl: apiResult.url,
              mp4Url: apiResult.mp4_url,
              apiMessage: apiResult.message,
              score: score
            });
            navigate('/results');
          } else {
            setShowLoadingDialog(false);
            throw new Error(apiResult.message || 'Video generation failed');
          }

        } catch (apiError) {
          console.error('API call failed:', apiError);
          await trackError('api_process_error', String(apiError), 'SurveyScreen');

          openSnackbar({
            text: "Không thể xử lý kết quả. Vui lòng thử lại sau.",
            type: "error"
          });
        }
      }
    } catch (error) {
      const code = (error as AppError).code;
      if (code === -201) {
        console.log("Người dùng đã từ chối cấp quyền");
        openSnackbar({
          text: "Vui lòng cấp quyền để tiếp tục xem kết quả",
          type: "error"
        });
      } else {
        console.log("Lỗi khác", error);
        openSnackbar({
          text: "Đã có lỗi xảy ra. Vui lòng thử lại",
          type: "error"
        });
      }
    }
  };

  const handleSubmit = async () => {
    // Track survey submit event
    await sendGA4Event({
      name: 'survey_submitted',
      params: {
        screen_name: 'SurveyScreen',
        button_location: 'footer',
        questions_answered: responses.length,
        button_text: 'XEM KẾT QUẢ'
      }
    });

    handleStartPhone();
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (showLoadingDialog) {
        // setShowLoadingDialog(false);
      }
    };
  }, [showLoadingDialog]);

  const getAnswerForQuestion = (questionId: string) => {
    const response = responses.find(r => r.questionId === questionId);
    return response?.answer || '';
  };

  const isQuestionAnswered = (question: Question) => {
    const answer = getAnswerForQuestion(question.id);

    if (question.type === 'checkbox') {
      return Array.isArray(answer) && answer.length > 0;
    }

    return answer !== '';
  };

  const areAllRequiredQuestionsAnswered = () => {
    return questions.every(question => isQuestionAnswered(question));
  };

  const testDialog = async () => {
    setShowLoadingDialog(true);
    try {
      // Get user ID from localStorage (stored during image upload)
      const gameState = localStorage.getItem('gameState');
      console.log(gameState);

      const userId = gameState ? JSON.parse(gameState).sessionId : uuidv4();
      console.log(userId);

      if (!userId) {
        throw new Error('User ID not found. Please restart the game.');
      }

      // Calculate score based on survey responses
      const score = calculateScore();

      // Call the new video generation API
      const apiResult = await generateVideo(userId, score);

      if (apiResult.status === 'success' && apiResult.url) {
        // Close loading dialog
        setShowLoadingDialog(false);

        // Pass the video URL and mp4 URL to the results screen
        onSurveyComplete(responses, {
          videoUrl: apiResult.url,
          mp4Url: apiResult.mp4_url,
          apiMessage: apiResult.message,
          score: score
        });

        navigate('/results');
      }
    } catch (error) {
      setShowLoadingDialog(false);
      console.error('Error processing results:', error);
      openSnackbar({
        text: "Không thể xử lý kết quả. Vui lòng thử lại sau.",
        type: "error"
      });
    }
  }

  const skipCallAPi = () => {
    navigate('/results');
  }


  return (
    <Page className="survey-screen">
      {/* Loading Dialog */}
      {showLoadingDialog && (
        <Box className="loading-dialog-overlay">
          <Box className="loading-dialog" style={{ backgroundImage: `url(${bgLoadingImage})`, backgroundSize: 'cover', backgroundPosition: 'center' }}>
            <Box className="loading-content">
              {/* <h3 className="loading-title gradient-text">
                {typedTitle.split('\n').map((line, index, array) => (
                  <React.Fragment key={index}>
                    {line}
                    {index < array.length - 1 && <br />}
                  </React.Fragment>
                ))}
              </h3>
              <Box className='!h-6'>
              <p className="loading-subtitle gradient-text">{typedSubtitle}</p>
              </Box> */}
              <h3 className="loading-title gradient-text">
                Phiếu bé ngoan <br /> đang được chuẩn bị
              </h3>
              <Box className='!h-6'>
                <p className="loading-subtitle gradient-text">Vui lòng chờ trong giây lát</p>
              </Box>

              {/* Notebook image element */}
              <Box className="mt-[-20px]">
                {/* DotLottie animation */}
                <DotLottieReact
                  className='w-[60%] m-auto'
                  src="https://lottie.host/53ea9a66-2bd8-45dd-a234-a7224b078732/fiJnanWbha.lottie"
                  loop
                  autoplay
                />

                {/* Flower loading GIF (plays once) */}
                <img
                  className='mt-[-60px] w-[60%] mx-auto'
                  src={flowerLoadingGif}
                  alt="Loading flower"
                  style={{ display: 'block' }}
                />
              </Box>
            </Box>
          </Box>
        </Box>
      )}
      {/* Header */}
      <GameHeader />

      {/* Main Content Area */}
      <Box className="main-content" style={{ position: 'relative' }}>
        {/* Hero Section */}
        <img
          src={flowerImageDecor}
          alt="Flower"
          style={{ position: 'absolute', top: '-16px', left: 0 }}
          className="h-32"
        />
        <img
          src={flowerImageDecor2}
          alt="Flower"
          style={{ position: 'absolute', top: '-32px', right: 0 }}
          className="h-28"
        />
        <Box className="hero-section">
          {/* Header Section */}
          <Box className="survey-header-section">
            <h3 className="gradient-text survey-line1">Bé ngoan sẽ được</h3>
            <h2 className="gradient-text survey-line2">bao nhiêu hoa hồng ?</h2>
            <p className="gradient-text survey-line3">
              Cùng trả lời <span className="number-highlight">5</span> câu hỏi sau đây
            </p>
          </Box>

          <img src={flowerImage} alt="Flower" className="flower-image" />

          <p className="question-note-text mb-4">* Có thể chọn nhiều đáp án</p>


          {/* Survey Body Section */}
          <Box
            className="survey-body-section"
            style={{ backgroundImage: `url(${surveyBgImage})` }}
          >
            {questions.map((question, index) => (
              <Box key={question.id} className="survey-question-item">
                {/* Question Number */}
                <Box
                  className="question-number-box"
                  style={{ backgroundImage: `url(${numberBgImage})` }}
                >
                  <span className="question-number-text">Câu {index + 1}</span>
                </Box>

                {/* Question Text */}
                <Box className="question-text-box">
                  <img
                    src={questionBgImage}
                    alt="Question background"
                    className="question-bg-image"
                  />
                  <p className="question-main-text">{question.question}</p>
                </Box>

                {/* Note Text */}

                {/* Answer Options */}
                <Box className="answer-options-container">
                  {question.options.map((option, optIndex) => {
                    const currentAnswer = getAnswerForQuestion(question.id);
                    const isChecked = Array.isArray(currentAnswer) && currentAnswer.includes(option);

                    return (
                      <Box key={optIndex} className="option-item">
                        <CustomCheckbox
                          checked={isChecked}
                          onChange={(checked) => handleCheckboxChange(question.id, option, checked)}
                          label={option}
                        />
                      </Box>
                    );

                  })}
                </Box>
              </Box>
            ))}
          </Box>
        </Box>
      </Box>

      {/* Selected Zone (Footer) - Only Action Buttons */}
      <Box className="selected-zone">
        <Box className="action-buttons">
          <button
            className="image-button"
            onClick={handleSubmit}
            disabled={!areAllRequiredQuestionsAnswered()}
          >
            <img src={selectButtonImage} alt="Submit Survey" />
            <span className="button-text gradient-text">XEM KẾT QUẢ</span>
          </button>
        </Box>
        {/* Brand Logo */}
        <img
          src={logoBrandImage}
          alt="Brand Logo"
          style={{
            position: 'absolute',
            bottom: '18px',
            right: '14px',
            width: 'auto',
            height: '20px',
            zIndex: 10
          }}
        />

        {/* QC */}
        <div
          style={{
            fontSize: '7px',
            color: 'white',
            position: 'absolute',
            bottom: '14px',
            left: '14px',
            width: 'auto',
            height: '20px',
            zIndex: 10
          }}>
          ENS-C-630-25
        </div>

      </Box>
    </Page>
  );
};

export default SurveyScreen;
