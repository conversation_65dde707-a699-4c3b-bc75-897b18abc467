// UTM Parameter Tracking Utilities for GA4 (accept ALL utm_*)
// Handles extraction, storage, and retrieval of UTM parameters

export interface UTMParameters {
  [key: string]: string | undefined; // mọi utm_* đều OK
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_term?: string;
  utm_content?: string;
}

const UTM_STORAGE_KEY = 'ga4_utm_params';
const UTM_EXPIRY_KEY = 'ga4_utm_expiry';
const UTM_EXPIRY_HOURS = 24;

const isUTMKey = (key: string): boolean => /^utm_/i.test(key);
const setIfUTM = (target: UTMParameters, key: string, val: any) => {
  if (val == null) return;
  if (!isUTMKey(key)) return;
  const k = key.toLowerCase().trim();
  if (k && target[k] === undefined) target[k] = String(val);
};

/**
 * Extract UTM parameters from URL / Zalo Mini App — accept ANY utm_*
 * Priority: URL search > URL hash > Zalo params > fallback scan
 */
export const extractUTMParameters = (): UTMParameters => {
  const params: UTMParameters = {};

  try {
    const isBrowser = typeof window !== 'undefined';
    const hasLocation = isBrowser && !!window.location;

    // 1) Search params (?a=1&b=2)
    if (hasLocation) {
      const urlParams = new URLSearchParams(window.location.search);
      urlParams.forEach((v, k) => setIfUTM(params, k, v));
    }

    // 2) Hash params (#utm_source=x&utm_medium=y)
    if (hasLocation && window.location.hash) {
      const hashParams = new URLSearchParams(window.location.hash.replace(/^#/, ''));
      hashParams.forEach((v, k) => setIfUTM(params, k, v));
    }

    // 3) Zalo Mini Program SDK (nếu có)
    const isZaloMiniProgram = isBrowser && (window as any).zalo !== undefined;
    if (isZaloMiniProgram) {
      const zalo = (window as any).zalo;
      if (zalo && typeof zalo.getQueryParams === 'function') {
        const zaloParams = zalo.getQueryParams();
        if (zaloParams && typeof zaloParams === 'object') {
          for (const k in zaloParams) {
            setIfUTM(params, k, (zaloParams as any)[k]);
          }
        }
      }
    }

    // 4) Fallback scan toàn bộ URL (phòng TH encoding khác thường)
    if (hasLocation && window.location.href) {
      const full = decodeURIComponent(window.location.href);
      const re = /[?&#]([^=&#]+)=([^&#]*)/g;
      let m: RegExpExecArray | null;
      // eslint-disable-next-line no-cond-assign
      while ((m = re.exec(full))) {
        const key = m[1];
        const val = m[2];
        setIfUTM(params, key, val);
      }
    }
  } catch (e) {
    console.warn('Error extracting UTM parameters:', e);
  }

  return params;
};

/** Store UTM to localStorage with expiry */
export const storeUTMParameters = (p: UTMParameters): void => {
  try {
    if (p && Object.keys(p).length > 0) {
      localStorage.setItem(UTM_STORAGE_KEY, JSON.stringify(p));
      const expiry = Date.now() + UTM_EXPIRY_HOURS * 60 * 60 * 1000;
      localStorage.setItem(UTM_EXPIRY_KEY, String(expiry));
    }
  } catch (e) {
    console.warn('Error storing UTM parameters:', e);
  }
};

/** Get stored (if not expired) */
export const getStoredUTMParameters = (): UTMParameters => {
  try {
    const expiryTime = localStorage.getItem(UTM_EXPIRY_KEY);
    if (expiryTime && Date.now() > parseInt(expiryTime, 10)) {
      localStorage.removeItem(UTM_STORAGE_KEY);
      localStorage.removeItem(UTM_EXPIRY_KEY);
      return {};
    }
    const raw = localStorage.getItem(UTM_STORAGE_KEY);
    if (raw) return JSON.parse(raw);
  } catch (e) {
    console.warn('Error retrieving UTM parameters:', e);
  }
  return {};
};

/** Current UTM: prefer URL; fallback storage */
export const getCurrentUTMParameters = (): UTMParameters => {
  const urlParams = extractUTMParameters();
  if (Object.keys(urlParams).length > 0) {
    storeUTMParameters(urlParams);
    return urlParams;
  }
  return getStoredUTMParameters();
};

/** Init on app start */
export const initUTMTracking = (): UTMParameters => {
  const params = getCurrentUTMParameters();
  if (Object.keys(params).length > 0) {
    console.log('UTM parameters initialized:', params);
  }
  return params;
};

/** Clear stored */
export const clearUTMParameters = (): void => {
  try {
    localStorage.removeItem(UTM_STORAGE_KEY);
    localStorage.removeItem(UTM_EXPIRY_KEY);
  } catch (e) {
    console.warn('Error clearing UTM parameters:', e);
  }
};

/** Format for GA4: pass through ALL utm_* as flat event params */
export const formatUTMForGA4 = (params: UTMParameters): Record<string, string> => {
  const out: Record<string, string> = {};
  for (const k in params) {
    if (isUTMKey(k) && params[k]) out[k] = String(params[k]);
  }
  return out;
};
