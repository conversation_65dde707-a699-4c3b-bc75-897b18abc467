import React, { useState } from 'react';
import { Box, Page, useNavigate, Modal, Text, useSnackbar } from 'zmp-ui';
import { chooseImage } from 'zmp-sdk/apis';
import { v4 as uuidv4 } from 'uuid';
import GameHeader from './GameHeader';
import selectButtonImage from '@/static/select-button.png';
import uploadPicImage from '@/static/upload/upload-pic.png';
import flowerImage from '@/static/flower.png';
import logoBrandImage from '@/static/logo-brand.png';
import { trackScreenView, trackImageUpload, trackError, sendGA4Event } from '@/utils/ga4-tracking';
import { storageData, uploadImageInBackground } from '@/utils/api';
import flowerImageDecor from '@/static/upload/flower-decor.png';

interface ImageUploadScreenProps {
  onImageUpload: (images: File[]) => void;
  uploadedImages: File[];
}

const ImageUploadScreen: React.FC<ImageUploadScreenProps> = ({
  onImageUpload,
  uploadedImages
}) => {
  const navigate = useNavigate();
  const [userName, setUserName] = useState('');
  const [uploading, setUploading] = useState(false);
  const [imageOrientation, setImageOrientation] = useState<'portrait' | 'landscape' | null>(null);
  const { openSnackbar } = useSnackbar();

  // Character length validation constants
  const MAX_NAME_LENGTH = 12;
  const MIN_NAME_LENGTH = 1; // Only require non-empty

  React.useEffect(() => {
    // Track screen view when component mounts
    trackScreenView('ImageUploadScreen', 'Game');
  }, []);

  // Handle name input change with validation
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;

    // Allow typing but limit to max length
    if (inputValue.length <= MAX_NAME_LENGTH) {
      setUserName(inputValue);
    }
  };

  // Get character count for display (handles Vietnamese characters properly)
  const getCharacterCount = (str: string): number => {
    // Use spread operator to properly count Unicode characters (including Vietnamese)
    return [...str].length;
  };

  // Check if name is valid
  const isNameValid = (): boolean => {
    const trimmedName = userName.trim();
    const charCount = getCharacterCount(trimmedName);
    return charCount > 0 && charCount <= MAX_NAME_LENGTH; // Must be non-empty and within max limit
  };




  const handleFiles = async (imageFile: File) => {
    setUploading(true);

    // Replace existing image with new one
    onImageUpload([imageFile]);

    // Reset orientation for new image
    setImageOrientation(null);

    // Track successful image upload
    const sessionId = localStorage.getItem('gameState') ?
      JSON.parse(localStorage.getItem('gameState') || '{}').sessionId :
      'unknown';
    const totalSize = imageFile.size;
    await trackImageUpload(sessionId, 1, totalSize);

    setUploading(false);
  };

  const handleFileSelect = async () => {
    await sendGA4Event({
      name: 'image_select_clicked',
      params: {
        screen_name: 'ImageUploadScreen',
        button_location: 'upload_area',
        current_image_count: uploadedImages.length
      }
    });

    try {
      // Use Zalo's chooseImage API
      const { filePaths } = await chooseImage({
        sourceType: ["album", "camera"],
        count: 1,
      });

      const filePath = filePaths[0];

      // Tạo đối tượng ảnh để đọc kích thước
      const img = new Image();
      img.src = filePath;

      img.onload = () => {
        console.log(`Chiều rộng ảnh nhận vào: ${img.naturalWidth}px`);
        console.log(`Chiều cao ảnh nhận vào: ${img.naturalHeight}px`);
      };

      if (filePaths && filePaths.length > 0) {
        // Fetch and convert the first file path to a File object
        const response = await fetch(filePaths[0]);
        const blob = await response.blob();

        // Extract filename from path or generate one
        const fileName = filePaths[0].split('/').pop() || `image_${Date.now()}.jpg`;

        // Create File object from blob
        const imageFile = new File([blob], fileName, {
          type: blob.type || 'image/jpeg',
          lastModified: Date.now()
        });

        await handleFiles(imageFile);
      }
    } catch (error) {
      console.error('Error choosing image:', error);
      await trackError('choose_image_error', String(error), 'ImageUploadScreen');
      openSnackbar({
        text: "Không thể chọn ảnh. Vui lòng thử lại.",
        type: "error",
        duration: 3000
      });
    }
  };

  const uploadImageToAPI = (): void => {
    // Get session ID from localStorage
    const gameState = localStorage.getItem('gameState');
    const sessionId = gameState ? JSON.parse(gameState).sessionId : uuidv4();

    // Store user info in localStorage for later use
    const currentGameState = JSON.parse(localStorage.getItem('gameState') || '{}');
    currentGameState.userId = sessionId;
    currentGameState.userName = userName.trim();
    localStorage.setItem('gameState', JSON.stringify(currentGameState));

    void storageData({
      id: sessionId,
      input_name: userName.trim(),
    }).catch(() => {
    });

    // Use centralized API function
    uploadImageInBackground(userName.trim(), sessionId, uploadedImages[0]);

    // Immediately navigate to survey without waiting
    navigate('/survey');
  };

  const handleNext = () => {
    if (uploadedImages.length > 0 && isNameValid()) {
      const trimmedName = userName.trim();

      // Additional validation check for empty name
      if (!trimmedName) {
        openSnackbar({
          text: `Vui lòng nhập tên của bạn`,
          type: "error",
          duration: 3000
        });
        return;
      }

      sendGA4Event({
        name: 'image_upload_submitted',
        params: {
          screen_name: 'ImageUploadScreen',
          button_location: 'footer',
          image_count: uploadedImages.length,
          user_name: trimmedName || 'not_provided'
        }
      });

      // Call the upload API (fire-and-forget)
      uploadImageToAPI();
    }
  };


  const handleImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const img = e.currentTarget;
    const orientation = img.naturalWidth > img.naturalHeight ? 'landscape' : 'portrait';
    setImageOrientation(orientation);

    // Clean up the object URL after the image loads
    URL.revokeObjectURL(img.src);
  };

  return (
    <Page className="image-upload-screen">
      {/* Header */}
      <GameHeader />

      {/* Main Content Area */}
      <Box className="main-content" style={{ position: 'relative' }}>
        <img
          src={flowerImageDecor}
          alt="Ensure Logo"
          style={{ position: 'absolute', top: '88px', left: 0 }}
          className="h-44"
        />
        <Box className='hero-section'>
          {/* Header Section with Title and Name Input */}
          <Box className="upload-header-section">
            <h2 className="gradient-text upload-title">
              Phiếu Bé Ngoan này của:
            </h2>

            <input
              type="text"
              value={userName}
              placeholder={`Nhập tên của bạn (ví dụ: Huyền Trang)`}
              onChange={handleNameChange}
              className={`name-input`}
              maxLength={MAX_NAME_LENGTH}
              required
            />
            <Box className="" style={{ position: 'relative' }}>
              <span
                className="dotted-placeholder"
              >
                ..........................................................................
              </span>

              <span
                style={{
                  position: 'absolute',
                  bottom: '-20px',
                  right: '0',
                  fontSize: '12px',
                  color: '#492405',
                  fontWeight: getCharacterCount(userName) >= MAX_NAME_LENGTH - 2 ? 'bold' : 'normal'
                }}
              >
                ({getCharacterCount(userName)}/{MAX_NAME_LENGTH})
              </span>

            </Box>
          </Box>

          <img src={flowerImage} alt="Flower" className="flower-image" />


          {/* Photo Section with Frame and Upload Area */}
          <Box className="photo-upload-section">
            <Box
              onClick={handleFileSelect}
              className="upload-frame-container"
            >
              <img
                src={uploadPicImage}
                alt="Upload frame"
                className="upload-frame"
              />

              {/* Display uploaded image */}
              {uploadedImages.length > 0 && uploadedImages[0] && (
                <Box className="uploaded-image-container">
                  <img
                    src={URL.createObjectURL(uploadedImages[0])}
                    alt="Uploaded"
                    className={`uploaded-image ${imageOrientation || ''}`}
                    onLoad={handleImageLoad}
                  />
                </Box>
              )}
            </Box>

          </Box>

          {/* Instructions Section */}
          <Box className="instructions-section">
            <p className="gradient-text instructions-text">
              Tải hình ảnh của bạn<br />
              chụp với Cha Mẹ
            </p>
          </Box>
        </Box>

      </Box>

      {/* Selected Zone (Footer) - Only Action Buttons */}
      <Box className="selected-zone">
        <Box className="action-buttons">
          <button
            className="image-button"
            onClick={handleNext}
            disabled={uploadedImages.length === 0 || !isNameValid()}
          >
            <img src={selectButtonImage} alt="Continue" />
            <span className="button-text gradient-text">TIẾP TỤC</span>
          </button>
        </Box>
        {/* Brand Logo */}
        <img
          src={logoBrandImage}
          alt="Brand Logo"
          style={{
            position: 'absolute',
            bottom: '18px',
            right: '14px',
            width: 'auto',
            height: '20px',
            zIndex: 10
          }}
        />

        {/* QC */}
        <div
          style={{
            fontSize: '7px',
            color: 'white',
            position: 'absolute',
            bottom: '14px',
            left: '14px',
            width: 'auto',
            height: '20px',
            zIndex: 10
          }}>
          ENS-C-630-25
        </div>
      </Box>

      {/* Error Modal for Image Orientation - Removed since we now accept all orientations */}
    </Page>
  );
};

export default ImageUploadScreen;
