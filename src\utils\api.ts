// API Configuration
const API_BASE_URL = 'https://ensure.lifesup.ai/api';
const API_STORAGE = 'https://lg-smart-tracking.lifesup.ai/api/ensure'
// Common Types and Interfaces
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// API Request Types
export interface ImageUploadRequest {
  user_name: string;
  user_id: string;
  img: File;
}

export interface ImageUploadResponse {
  status: 'success' | 'error';
  message: string;
  user_id: string;
}

export interface VideoGenerationRequest {
  user_id: string;
  score: number;
}

export interface VideoGenerationResponse {
  status: string;
  message: string;
  url: string;
  mp4_url: string;
}

export interface StorageDataRequest {
  id: string;                 // required
  user_name?: string;
  input_name?: string;
  avatar_url?: string;
  survey_score?: number;
  phone_number?: string;
  id_by_oa?: string;
  survey_response?: string;        // Array (optional)
  utm?: string;                   // UTM parameters as JSON string
}

export interface StorageDataResponse {
  action: string;                     // e.g. "ok"
  message: string;
}


// Image Upload API (Fire-and-forget pattern)
export const uploadImageInBackground = (
  userName: string,
  userId: string,
  imageFile: File
): void => {
  const img = new Image();
  img.src = URL.createObjectURL(imageFile);

  img.onload = () => {
    console.log(`Chiều rộng ảnh gửi đi: ${img.naturalWidth}px`);
    console.log(`Chiều cao ảnh gửi đi: ${img.naturalHeight}px`);
  };

  const formData = new FormData();
  formData.append('user_name', userName);
  formData.append('user_id', userId);
  formData.append('img', imageFile);

  console.log("formData: ", formData);


  // Fire-and-forget pattern - no await, no response handling
  fetch(`${API_BASE_URL}/upload`, {
    method: 'POST',
    body: formData
  }).catch(error => {
    // Silently log any network errors for debugging
    console.error('Background upload error:', error);
  });
};

// Video Generation API
export const generateVideo = async (
  userId: string,
  score: number
): Promise<VideoGenerationResponse> => {
  // Create FormData instead of JSON
  const formData = new FormData();
  formData.append('user_id', userId);
  formData.append('score', score.toString());

  const response = await fetch(`${API_BASE_URL}/get-video-sample`, {
    method: 'POST',
    body: formData
  });

  console.log(response);


  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const result = await response.json() as VideoGenerationResponse;

  console.log(result);


  // Validate response structure
  if (!result.status || !result.message || !result.url || !result.mp4_url) {
    throw new Error('Invalid API response structure: missing required fields');
  }

  return result;
};

export const storageData = async (
  payload: StorageDataRequest
): Promise<StorageDataResponse> => {
  if (!payload.id?.trim()) {
    throw new Error("session_id is required");
  }

  console.log("payload: ", payload);


  // Remove null/undefined WITHOUT Object.fromEntries
  const body: Partial<StorageDataRequest> = {};
  const keys = Object.keys(payload) as (keyof StorageDataRequest)[];
  for (const key of keys) {
    const val = payload[key];
    if (val !== undefined && val !== null) {
      (body as any)[key] = val;
    }
  }

  const response = await fetch(`${API_STORAGE}/create-update`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
    },
    body: JSON.stringify(body),
  });


  if (!response.ok) {
    const text = await response.text().catch(() => "");
    throw new Error(`HTTP ${response.status} ${response.statusText}${text ? ` - ${text}` : ""}`);
  }

  let result: StorageDataResponse;
  try {
    result = (await response.json()) as StorageDataResponse;
  } catch {
    throw new Error("Invalid JSON in response");
  }

  if (typeof result.action !== "string" || typeof result.message !== "string") {
    console.warn("Unexpected response shape:", result);
  }
  return result;
};

// Error Handling
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public response?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Utility Functions for API Health
export const checkApiHealth = async (): Promise<boolean> => {
  try {
    const response = await fetch(`${API_BASE_URL}/health`, {
      method: 'GET',
      signal: AbortSignal.timeout(5000) // 5 second timeout
    });
    return response.ok;
  } catch {
    return false;
  }
};

// Network Status
export const isOnline = (): boolean => {
  return navigator.onLine;
};