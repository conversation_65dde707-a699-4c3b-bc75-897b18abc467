// Welcome Screen Styles

.welcome-screen {
  padding-bottom: 40px;
  height: 100%;

  .action-buttons {
    max-width: 600px;
    width: 100%;
    margin: 0 auto;

  }

  .main-content {
    .ensure-logo {
      width: 80px;
      height: auto;
      margin: 0;
    }

    .decor-image {
      width: 60px;
      height: auto;
      margin: 0;
    }
  }

  .hero-section {

    .primary-heading {
      font-weight: 900;
      font-style: italic;
      font-size: 28px;
      line-height: 125%;
      letter-spacing: 0;
      text-align: center;
      margin: 0;
    }

    .secondary-text {
      font-weight: 700;
      font-style: italic;
      font-size: 14px;
      line-height: 125%;
      letter-spacing: 0;
      text-align: center;
      margin: 0;
    }

    .hero-image {
      height: 20vh;
      max-width: 300px;
      margin: var(--spacing-md) auto;
      display: block;
    }

    .intro-text {
      font-weight: 700;
      font-style: italic;
      font-size: 12px;
      line-height: 16px;
      text-align: center;
      margin: var(--spacing-md) 0;
    }

    .cta-primary {
      font-size: 14px;
      font-style: italic;
      font-weight: 700;
      line-height: 100%;
      text-align: center;
      margin: var(--spacing-sm) 0;
      margin-bottom: 4px;
    }

    .cta-secondary {
      font-size: 12px;
      font-style: italic;
      font-weight: 700;
      line-height: 118%;
      text-align: center;
      margin: 0;
    }
  }

  // Terms and Conditions Checkbox
  .terms-checkbox-container {
    margin-top: -4px;
    margin-bottom: 24px;
    text-align: center;

    .terms-text {
      color: #023C81;
      font-weight: 500;
      font-style: normal;
      font-size: 9px;
      line-height: 100%;
      letter-spacing: 0;

      .terms-link {
        text-decoration: underline;
        cursor: pointer;
        color: inherit;

        &:hover {
          opacity: 0.8;
        }
      }
    }

    .terms-error-message {
      display: block;
      color: #ff6b6b;
      font-size: 10px;
      font-weight: 500;
      margin-top: 8px;
      animation: fadeIn 0.3s ease;
    }
  }

  // Terms Modal Styles (matching download dialog)
  .terms-modal {
    max-height: 80vh;
    position: relative;

    .zaui-modal-content {
      background: transparent !important;
      padding: 0 !important;
      max-width: 90vw;
      width: 100%;
      max-width: 400px;
    }

    .terms-dialog {
      background: radial-gradient(50% 219.26% at 50% 50%, #133985 0%, #023C81 71.63%, #1C2664 100%);
      border-radius: 16px;
      padding: 24px;
      text-align: center;
      position: relative;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
      max-height: 70vh;
      overflow-y: auto;

      .close-button {
        position: absolute;
        top: 0;
        right: 0;
        transform: translate(-10%, 10%);
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: linear-gradient(180deg, #DCAD5D 0%, #DED2A4 25%, #D8BC66 76.44%, #DCAD5D 100%);
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
        transition: all 0.2s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

        svg {
          width: 14px;
          height: 14px;
        }
      }

      .terms-dialog-content {
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      .terms-heading {
        font-size: 20px;
        font-weight: 900;
        font-style: italic;
        line-height: 28px;
        text-align: center;
        margin-bottom: 20px;
      }

      .terms-content {
        width: 100%;
        margin-top: 16px;

        p {
          font-size: 12px;
          line-height: 1.6;
          color: #ffffff;
          text-align: left;
          margin-bottom: 16px;
          font-weight: 400;

          &:last-child {
            margin-bottom: 0;
          }
        }

        strong {
          font-weight: 600;
          color: #DCAD5D;
        }
      }
    }

    @media (max-width: 768px) {
      .terms-dialog {
        .close-button {
          width: 28px;
          height: 28px;

          svg {
            width: 12px;
            height: 12px;
          }
        }
      }
    }

    @media (max-width: 480px) {
      .terms-dialog {
        padding: 20px;

        .close-button {
          width: 24px;
          height: 24px;

          svg {
            width: 10px;
            height: 10px;
          }
        }

        .terms-heading {
          font-size: 18px;
          line-height: 24px;
        }

        .terms-content {
          p {
            font-size: 11px;
          }
        }
      }
    }
  }

  // Golden gradient text class
  .gradient-text-gold {
    background: linear-gradient(87.79deg, #DBB664 1.14%, #E9E197 8.06%, #DBB664 16.34%, #EBD57A 30.5%, #DBB664 38.21%, #E9E197 51.61%, #DBB664 61.5%, #EBD57A 71.2%, #DBB664 85.72%, #D0A556 99.37%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
  }
}

.image-button {
  position: relative;
  display: inline-block;
  border: none;
  background: transparent;
  padding: 0;
  cursor: pointer;
  margin: 0;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: buttonPulse 3s ease-in-out infinite;

  img {
    margin: auto;
    display: block;
    width: 85%;
    height: auto;
    max-width: 300px;
    filter: drop-shadow(0 6px 16px rgba(0, 0, 0, 0.25)) drop-shadow(0 0 20px rgba(219, 182, 100, 0.3));
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }

  .button-text {
    width: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--white);
    text-align: center;
    leading-trim: both;
    text-edge: cap;
    font-size: 20px;
    font-style: normal;
    font-weight: 900;
    line-height: 24px;
    pointer-events: none;
    white-space: nowrap;
    padding-bottom: 10px;
    padding-left: 40px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease;
  }

  // Hover effect - more dramatic
  &:hover:not(:disabled) {
    transform: translateY(-4px) scale(1.05);
    animation: none; // Stop pulse animation on hover

    img {
      filter: drop-shadow(0 12px 24px rgba(0, 0, 0, 0.4)) drop-shadow(0 0 30px rgba(219, 182, 100, 0.6)) brightness(1.15) saturate(1.1);
    }

    .button-text {
      transform: translate(-50%, -50%) scale(1.05);
      text-shadow: 0 3px 6px rgba(0, 0, 0, 0.6),
        0 0 15px rgba(255, 255, 255, 0.3);
    }
  }

  // Active/pressed effect - bouncy with vibration
  &:active:not(:disabled) {
    transform: translateY(-1px) scale(1.02);
    transition: all 0.1s ease;
    animation: buttonVibrate 0.3s ease-in-out;

    img {
      filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.4)) drop-shadow(0 0 15px rgba(219, 182, 100, 0.4)) brightness(1.1);
    }

    .button-text {
      transform: translate(-50%, -50%) scale(1.02);
      animation: textBounce 0.3s ease-in-out;
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    animation: none;
  }

  // Add shimmer effect overlay
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent);
    transition: left 0.6s ease;
    z-index: 1;
    pointer-events: none;
  }

  &:hover::before {
    left: 100%;
  }

  // Enhanced button specific styles
  &.enhanced-button {
    .button-glow {
      position: absolute;
      top: -10px;
      left: -10px;
      right: -10px;
      bottom: -10px;
      background: radial-gradient(circle, rgba(219, 182, 100, 0.3) 0%, transparent 70%);
      border-radius: 50%;
      opacity: 0;
      transition: opacity 0.3s ease;
      z-index: -1;
      animation: glowPulse 2s ease-in-out infinite;
    }

    .button-sparkles {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      pointer-events: none;
      z-index: 2;

      .sparkle {
        position: absolute;
        font-size: 16px;
        opacity: 0;
        animation: sparkleFloat 3s ease-in-out infinite;

        &.sparkle-1 {
          top: 10%;
          left: 15%;
          animation-delay: 0s;
        }

        &.sparkle-2 {
          top: 20%;
          right: 20%;
          animation-delay: 0.8s;
        }

        &.sparkle-3 {
          bottom: 25%;
          left: 20%;
          animation-delay: 1.6s;
        }

        &.sparkle-4 {
          bottom: 15%;
          right: 15%;
          animation-delay: 2.4s;
        }
      }
    }

    &:hover {
      .button-glow {
        opacity: 1;
        animation: glowPulseHover 1s ease-in-out infinite;
      }

      .button-sparkles .sparkle {
        animation-duration: 1.5s;
      }
    }
  }
}

.selected-zone-welcome {
  background-image: url('../static/select-zone-welcome.png');
  padding: var(--spacing-lg);
  padding-bottom: 0px;
  position: absolute;

  width: 100%;
  min-height: 100px; // Ensure background image is visible
  z-index: 10;
}