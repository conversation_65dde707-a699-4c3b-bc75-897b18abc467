// Welcome Screen Styles

.welcome-screen {
  .action-buttons {
    max-width: 600px;
    width: 100%;
    margin: 0 auto;
  }

  .main-content {
    .ensure-logo {
      width: 80px;
      height: auto;
      margin: 0;
    }

    .decor-image {
      width: 60px;
      height: auto;
      margin: 0;
    }
  }

  .hero-section {

    .primary-heading {
      font-weight: 900;
      font-style: italic;
      font-size: 32px;
      line-height: 125%;
      letter-spacing: 0;
      text-align: center;
      margin: 0 0 var(--spacing-sm) 0;
    }

    .secondary-text {
      font-weight: 700;
      font-style: italic;
      font-size: 17px;
      line-height: 125%;
      letter-spacing: 0;
      text-align: center;
      margin: 0;
    }

    .hero-image {
      width: 100%;
      max-width: 300px;
      height: auto;
      margin: var(--spacing-lg) auto;
      display: block;
    }

    .intro-text {
      font-weight: 700;
      font-style: italic;
      font-size: 12px;
      line-height: 16px;
      text-align: center;
      margin: var(--spacing-md) 0;
    }

    .cta-primary {
      font-size: 18px;
      font-style: italic;
      font-weight: 700;
      line-height: 22px;
      text-align: center;
      margin: var(--spacing-sm) 0;
      margin-bottom: 4px;
    }

    .cta-secondary {
      font-size: 16px;
      font-style: italic;
      font-weight: 700;
      line-height: 20px;
      text-align: center;
      margin: 0;
    }
  }
  
  // Terms and Conditions Checkbox
  .terms-checkbox-container {
    margin-bottom: -12px;
    text-align: center;
    
    .terms-checkbox-label {
      display: inline-flex;
      align-items: center;
      gap: 4px;
      cursor: pointer;
      user-select: none;
      
      .terms-checkbox {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        width: 12px;
        height: 12px;
        border: 2px solid #ffffff;
        border-radius: 50%;
        background-color: transparent;
        cursor: pointer;
        position: relative;
        transition: all 0.2s ease;
        flex-shrink: 0;
        
        &:checked {
          background-color: #ffffff;

        }
        
        &:focus {
          outline: none;
          box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
        }
      }
      
      .terms-text {
        color: #ffffff;
        font-weight: 500;
        font-style: normal;
        font-size: 8px;
        line-height: 100%;
        letter-spacing: 0;
        
        .terms-link {
          text-decoration: underline;
          cursor: pointer;
          color: inherit;
          
          &:hover {
            opacity: 0.8;
          }
        }
      }
    }
    
    .terms-error-message {
      display: block;
      color: #ff6b6b;
      font-size: 10px;
      font-weight: 500;
      margin-top: 8px;
      animation: fadeIn 0.3s ease;
    }
  }
  
  // Terms Modal Styles (matching download dialog)
  .terms-modal {
    max-height: 80vh;

    .zaui-modal-content {
      background: transparent !important;
      padding: 0 !important;
      max-width: 90vw;
      width: 100%;
      max-width: 400px;
    }
    
    .terms-dialog {
      background: radial-gradient(50% 219.26% at 50% 50%, #133985 0%, #023C81 71.63%, #1C2664 100%);
      border-radius: 16px;
      padding: 24px;
      text-align: center;
      position: relative;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
      max-height: 80vh;
      overflow-y: auto;
      
      .close-button {
        position: absolute;
        top: 0;
        right: 0;
        transform: translate(50%, -50%);
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: linear-gradient(180deg, #DCAD5D 0%, #DED2A4 25%, #D8BC66 76.44%, #DCAD5D 100%);
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
        transition: all 0.2s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        
        &:hover {
          transform: translate(50%, -50%) scale(1.1);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        svg {
          width: 14px;
          height: 14px;
        }
      }
      
      .terms-dialog-content {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      
      .terms-heading {
        font-size: 20px;
        font-weight: 900;
        font-style: italic;
        line-height: 28px;
        text-align: center;
        margin-bottom: 20px;
      }
      
      .terms-content {
        width: 100%;
        margin-top: 16px;
        
        p {
          font-size: 12px;
          line-height: 1.6;
          color: #ffffff;
          text-align: left;
          margin-bottom: 16px;
          font-weight: 400;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
        
        strong {
          font-weight: 600;
          color: #DCAD5D;
        }
      }
    }
    
    @media (max-width: 768px) {
      .terms-dialog {
        .close-button {
          width: 28px;
          height: 28px;
          
          svg {
            width: 12px;
            height: 12px;
          }
        }
      }
    }
    
    @media (max-width: 480px) {
      .terms-dialog {
        padding: 20px;
        
        .close-button {
          width: 24px;
          height: 24px;
          
          svg {
            width: 10px;
            height: 10px;
          }
        }
        
        .terms-heading {
          font-size: 18px;
          line-height: 24px;
        }
        
        .terms-content {
          p {
            font-size: 11px;
          }
        }
      }
    }
  }
  
  // Golden gradient text class
  .gradient-text-gold {
    background: linear-gradient(87.79deg, #DBB664 1.14%, #E9E197 8.06%, #DBB664 16.34%, #EBD57A 30.5%, #DBB664 38.21%, #E9E197 51.61%, #DBB664 61.5%, #EBD57A 71.2%, #DBB664 85.72%, #D0A556 99.37%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
  }
}