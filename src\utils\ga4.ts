// GA4 Adaptive Analytics using zmp-ga4
import { AdaptiveAnalytics } from "zmp-ga4";

// GA4 Configuration
const MEASUREMENT_ID = "G-SSFXY6G7HT";
const API_SECRET = "4414522757";

// Initialize AdaptiveAnalytics
export const ga4 = new AdaptiveAnalytics(
  MEASUREMENT_ID,
  API_SECRET,
  {
    // Use Measurement Protocol for zbrowser:// protocol or iOS
    useMeasurementProtocolWhen: () => {
        return true;
    },
    // Disable automatic page view tracking as we'll handle it manually
    gtagConfig: {
      send_page_view: false,
    },
  }
);

// Helper function to check if we're in Zalo Mini App
export const isZaloMiniApp = (): boolean => {
  return typeof window !== 'undefined' && 
         (window.location.protocol === 'zbrowser:' || window.zalo !== undefined);
};

// Helper function to get client ID
export const getClientId = (): string => {
  try {
    let clientId = localStorage.getItem('ga4_client_id');
    if (!clientId) {
      clientId = `${Date.now()}.${Math.random().toString(36).substring(2, 15)}`;
      localStorage.setItem('ga4_client_id', clientId);
    }
    return clientId;
  } catch (e) {
    return `${Date.now()}.${Math.random().toString(36).substring(2, 15)}`;
  }
};

// Helper function to get session ID
export const getSessionId = (): string => {
  try {
    const sessionKey = 'ga4_session_id';
    const sessionExpiry = 'ga4_session_expiry';
    
    const now = Date.now();
    const expiry = localStorage.getItem(sessionExpiry);
    
    if (!expiry || now > parseInt(expiry)) {
      const newSessionId = Math.random().toString(36).substring(2, 15);
      localStorage.setItem(sessionKey, newSessionId);
      localStorage.setItem(sessionExpiry, (now + 30 * 60 * 1000).toString());
      return newSessionId;
    }
    
    return localStorage.getItem(sessionKey) || '';
  } catch (e) {
    return Math.random().toString(36).substring(2, 15);
  }
};

// Export the main tracking function
export const trackEvent = (eventName: string, params?: Record<string, any>): void => {
  const enhancedParams = {
    session_id: getSessionId(),
    engagement_time_msec: 100,
    platform: isZaloMiniApp() ? 'zalo_mini_app' : 'web',
    ...params
  };
  
  // Log in development
  if (window.location.hostname === 'localhost') {
    console.log('GA4 Event:', {
      name: eventName,
      params: enhancedParams,
      transport: window.location.protocol === 'zbrowser:' ? 'measurement_protocol' : 'gtag'
    });
  }
  
  ga4.trackEvent(eventName, enhancedParams);
};