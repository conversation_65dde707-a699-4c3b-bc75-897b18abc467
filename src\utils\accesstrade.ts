const AFF_SID_STORAGE_KEY = 'accesstrade_aff_sid';
// 👉 Đổi endpoint BE theo app của bạn
const BACKEND_URL = 'https://lg-smart-tracking.lifesup.ai/api/ensure/webhook';

// Tạo chuỗi ISO local "YYYY-MM-DDTHH:mm:ss"
const isoLocalNow = (): string => {
  const now = new Date();
  const local = new Date(now.getTime() - now.getTimezoneOffset() * 60000);
  return local.toISOString().slice(0, 19);
};

export const getAffSidFromLocalStorage = (): string | null => {
  try {
    return localStorage.getItem(AFF_SID_STORAGE_KEY);
  } catch (error) {
    console.error('Error reading aff_sid from localStorage:', error);
    return null;
  }
};

export const storeAffSidToLocalStorage = (affSid: string): void => {
  try {
    localStorage.setItem(AFF_SID_STORAGE_KEY, affSid);
  } catch (error) {
    console.error('Error storing aff_sid to localStorage:', error);
  }
};

type AccessTradePayload = {
  conversion_id: string;
  conversion_result_id: string;
  tracking_id: string;
  transaction_id: string;
  transaction_time: string; // "YYYY-MM-DDTHH:mm:ss"
  transaction_value: number;
  transaction_discount: number;
  is_cpql: number;
};

export const sendAccessTradePostback = async (sessionId: string): Promise<void> => {
  const affSid = getAffSidFromLocalStorage();
  if (!affSid) {
    console.log('No aff_sid found in localStorage, skipping AccessTrade postback');
    return;
  }

  // ❌ Không cần apiKey ở client nữa (BE sẽ giữ key nếu cần)
  const payload: AccessTradePayload = {
    conversion_id: sessionId,
    conversion_result_id: '30',
    tracking_id: affSid,
    transaction_id: sessionId,
    transaction_time: isoLocalNow(),
    transaction_value: 0,
    transaction_discount: 0,
    is_cpql: 1,
  };

  // (Tuỳ ý) Timeout để tránh treo request
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 10000); // 10s

  try {
    const response = await fetch(BACKEND_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }, // ✅ chỉ gửi body JSON
      body: JSON.stringify(payload),
      signal: controller.signal,
      // credentials: 'include', // bật nếu BE cần cookie/SSO cùng domain
    });

    const resText = await response.text();
    clearTimeout(timeoutId);

    if (!response.ok) {
      console.error('❌ Backend Proxy Error:', {
        status: response.status,
        statusText: response.statusText,
        requestUrl: BACKEND_URL,
        payloadSent: payload,
        responseBody: resText,
      });
      return;
    }

    console.log('✅ Sent to backend successfully:', { payload, resText });
  } catch (error: any) {
    clearTimeout(timeoutId);
    const hint =
      error?.name === 'AbortError'
        ? 'Request timed out (abort). Kiểm tra network/timeout ở BE.'
        : error?.message === 'Failed to fetch'
        ? 'Có thể do CORS của BE hoặc domain chưa whitelist trong Zalo.'
        : 'Lỗi mạng khác.';

    console.error('🚨 Network/Fetch Error:', {
      message: error?.message,
      hint,
      requestUrl: BACKEND_URL,
      payloadSent: payload,
      stack: error?.stack,
    });
  }
};
