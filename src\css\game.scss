// Game Application Styles
// Mobile-first responsive design with CSS Grid and Flexbox

// Import modular SCSS files
@import 'reset'; // CSS Reset - must be first
@import 'fonts'; // Font declarations
@import 'variables';
@import 'layout';
@import 'welcome';
@import 'upload';
@import 'survey';
@import 'results';


// Reusable gradient text class
.gradient-text {
  background: linear-gradient(87.79deg, #175AA1 1.14%, #257EC2 8.06%, #124C95 16.34%, #237ABE 30.5%, #145099 38.21%,
      #2379BE 51.61%, #124B94 61.5%, #257EC2 71.2%, #124B94 85.72%, #124B94 99.37%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  filter: drop-shadow(0px 0.5px 0px #655F3E);
}

.flower-image {
  width: 150px;
  height: auto;
  margin: var(--spacing-sm) auto var(--spacing-md);
  display: block;
}

.hero-section {
  text-align: center;
  margin-bottom: var(--spacing-xxl);
}

// Animation for modal slide-in effect
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Button pulse animation for attention-grabbing effect
@keyframes buttonPulse {

  0%,
  100% {
    transform: scale(1);
    filter: drop-shadow(0 6px 16px rgba(0, 0, 0, 0.25)) drop-shadow(0 0 20px rgba(219, 182, 100, 0.3));
  }

  50% {
    transform: scale(1.02);
    filter: drop-shadow(0 8px 20px rgba(0, 0, 0, 0.3)) drop-shadow(0 0 25px rgba(219, 182, 100, 0.5));
  }
}

// Floating animation for subtle movement
@keyframes buttonFloat {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-3px);
  }
}

// Glow pulse animation
@keyframes glowPulse {

  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }

  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

// Enhanced glow on hover
@keyframes glowPulseHover {

  0%,
  100% {
    opacity: 0.8;
    transform: scale(1.1);
  }

  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

// Sparkle floating animation
@keyframes sparkleFloat {
  0% {
    opacity: 0;
    transform: translateY(0px) rotate(0deg) scale(0.8);
  }

  25% {
    opacity: 1;
    transform: translateY(-10px) rotate(90deg) scale(1);
  }

  50% {
    opacity: 0.8;
    transform: translateY(-15px) rotate(180deg) scale(1.1);
  }

  75% {
    opacity: 0.6;
    transform: translateY(-10px) rotate(270deg) scale(1);
  }

  100% {
    opacity: 0;
    transform: translateY(0px) rotate(360deg) scale(0.8);
  }
}

// Button vibration on click
@keyframes buttonVibrate {

  0%,
  100% {
    transform: translateY(-1px) scale(1.02);
  }

  10% {
    transform: translateY(-1px) scale(1.02) translateX(-1px);
  }

  20% {
    transform: translateY(-1px) scale(1.02) translateX(1px);
  }

  30% {
    transform: translateY(-1px) scale(1.02) translateX(-1px);
  }

  40% {
    transform: translateY(-1px) scale(1.02) translateX(1px);
  }

  50% {
    transform: translateY(-1px) scale(1.02) translateX(-0.5px);
  }

  60% {
    transform: translateY(-1px) scale(1.02) translateX(0.5px);
  }

  70% {
    transform: translateY(-1px) scale(1.02) translateX(-0.5px);
  }

  80% {
    transform: translateY(-1px) scale(1.02) translateX(0.5px);
  }

  90% {
    transform: translateY(-1px) scale(1.02);
  }
}

// Text bounce animation
@keyframes textBounce {
  0% {
    transform: translate(-50%, -50%) scale(1.02);
  }

  25% {
    transform: translate(-50%, -50%) scale(1.08);
  }

  50% {
    transform: translate(-50%, -50%) scale(1.05);
  }

  75% {
    transform: translate(-50%, -50%) scale(1.07);
  }

  100% {
    transform: translate(-50%, -50%) scale(1.02);
  }
}