// Game Application Styles
// Mobile-first responsive design with CSS Grid and Flexbox

// Import modular SCSS files
@import 'reset'; // CSS Reset - must be first
@import 'fonts'; // Font declarations
@import 'variables';
@import 'layout';
@import 'welcome';
@import 'upload';
@import 'survey';
@import 'results';


// Reusable gradient text class
.gradient-text {
  background: linear-gradient(87.79deg, #175AA1 1.14%, #257EC2 8.06%, #124C95 16.34%, #237ABE 30.5%, #145099 38.21%,
      #2379BE 51.61%, #124B94 61.5%, #257EC2 71.2%, #124B94 85.72%, #124B94 99.37%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  filter: drop-shadow(0px 0.5px 0px #655F3E);
}

.flower-image {
  width: 150px;
  height: auto;
  margin: var(--spacing-sm) auto;
  display: block;
}

.hero-section {
  text-align: center;
  margin-bottom: var(--spacing-xxl);
}

// Animation for modal slide-in effect
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}