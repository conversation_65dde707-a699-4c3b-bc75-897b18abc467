import React, { useRef, useState } from 'react';
import { Box, Page, Text, Modal, useSnackbar } from 'zmp-ui';
import { openShareSheet, saveVideoToGallery } from 'zmp-sdk';
import GameHeader from './GameHeader';
import selectButtonImage2 from '@/static/select-button-2.png';
import selectButtonImage from '@/static/select-button.png'
import heroImage from '@/static/welcome/hero.png';
import flowerImage from '@/static/flower.png';
import prizesImage from '@/static/prize/prizes.png';
import logoBrandImage from '@/static/logo-brand.png';
import { shareResults } from '@/utils/gameUtils';
import { trackScreenView, trackShare, trackGameRestart, trackGameComplete, trackError, trackCustomEvent, sendGA4Event } from '@/utils/ga4-tracking';
import flowerImageDecor from '@/static/survey/flower-decor.png';
import PrizesSection from './PrizesSection';
interface ResultsScreenProps {
  uploadedImages: File[];
  surveyResponses: any[];
  userData?: {
    name?: string;
    avatar?: string;
    idByOA?: string | null;
    mobile?: string | null;
    videoUrl?: string;
    mp4Url?: string;
    apiMessage?: string;
  };
  onRestart: () => void;
}

const ResultsScreen: React.FC<ResultsScreenProps> = ({
  uploadedImages,
  surveyResponses,
  userData,
  onRestart
}) => {
  const { openSnackbar } = useSnackbar();
  const [showDownloadDialog, setShowDownloadDialog] = useState(false);
  const [videoError, setVideoError] = useState<string | false>(false);
  const [videoLoading, setVideoLoading] = useState(true);
  const [retryCount, setRetryCount] = useState(0);
  const [networkStatus, setNetworkStatus] = useState(navigator.onLine);
  const [videoUrlTested, setVideoUrlTested] = useState(false);
  const [lastRetryTime, setLastRetryTime] = useState(0);
  const [validationStartTime] = useState(Date.now());
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);
  // Platform detection from localStorage
  const [platform, setPlatform] = useState<'android' | 'ios' | 'unknown'>('unknown');

  React.useEffect(() => {
    // Get platform from system info stored in localStorage
    const systemInfoStr = localStorage.getItem('systemInfo');
    if (systemInfoStr) {
      try {
        const systemInfo = JSON.parse(systemInfoStr);
        const detectedPlatform = systemInfo.platform?.toLowerCase();

        if (detectedPlatform === 'android') {
          setPlatform('android');
        } else if (detectedPlatform === 'ios') {
          setPlatform('ios');
        } else {
          setPlatform('unknown');
        }
      } catch (error) {
        console.error('Error parsing system info:', error);
        setPlatform('unknown');
      }
    }
  }, []);

  React.useEffect(() => {
    // Track screen view when component mounts
    trackScreenView('ResultsScreen', 'Game');

    // Track game completion
    const sessionId = localStorage.getItem('gameState') ?
      JSON.parse(localStorage.getItem('gameState') || '{}').sessionId :
      'unknown';
    const gameState = JSON.parse(localStorage.getItem('gameState') || '{}');
    const startTime = gameState.startTime ? new Date(gameState.startTime).getTime() : Date.now();
    const completionTime = Math.floor((Date.now() - startTime) / 1000);

    // Use default score value of 1
    const score = 1;
    const category = 'Người con hiếu thảo';

    trackGameComplete(sessionId, score, category, completionTime);

    // Monitor network status
    const handleOnline = () => setNetworkStatus(true);
    const handleOffline = () => setNetworkStatus(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Test video URL accessibility when component mounts
  React.useEffect(() => {
    const testVideoUrl = async () => {
      // Validate preconditions
      if (!userData?.videoUrl || videoUrlTested) {
        if (!userData?.videoUrl) {
          setVideoError('processing');
          setVideoLoading(false);
        }
        return;
      }

      // Check URL format validity
      if (!isValidUrl(userData.videoUrl)) {
        setVideoError('invalid_url');
        setVideoLoading(false);
        await trackError('video_url_invalid_format', `Invalid URL format: ${userData.videoUrl}`, 'ResultsScreen');
        return;
      }

      setVideoUrlTested(true);
      const testStartTime = Date.now();

      try {
        // Create timeout promise
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Request timeout')), 10000); // 10 second timeout
        });

        // Use HEAD request to test URL without downloading full content
        const fetchPromise = fetch(userData.videoUrl, {
          method: 'HEAD',
          mode: 'cors',
          cache: 'no-cache',
          headers: {
            'Accept': 'video/*'
          }
        });

        // Race between fetch and timeout
        const response = await Promise.race([fetchPromise, timeoutPromise]) as Response;
        const responseTime = Date.now() - testStartTime;

        if (!response.ok) {
          // Handle different HTTP error codes
          if (response.status === 404) {
            setVideoError('not_found');
            await trackError('video_url_404', userData.videoUrl, 'ResultsScreen', {
              response_time: responseTime,
              retry_count: retryCount
            });
          } else if (response.status === 403 || response.status === 401) {
            setVideoError('access_denied');
            await trackError('video_url_access_denied', `${response.status}: ${userData.videoUrl}`, 'ResultsScreen', {
              response_time: responseTime,
              retry_count: retryCount
            });
          } else if (response.status >= 500) {
            setVideoError('server_error');
            await trackError('video_url_server_error', `${response.status}: ${userData.videoUrl}`, 'ResultsScreen', {
              response_time: responseTime,
              retry_count: retryCount
            });
          } else if (response.status === 429) {
            setVideoError('rate_limited');
            await trackError('video_url_rate_limited', userData.videoUrl, 'ResultsScreen', {
              response_time: responseTime,
              retry_count: retryCount
            });
          } else {
            setVideoError('unknown_error');
            await trackError('video_url_http_error', `${response.status}: ${userData.videoUrl}`, 'ResultsScreen', {
              response_time: responseTime,
              retry_count: retryCount
            });
          }
          setVideoLoading(false);
        } else {
          // Success - video URL is accessible
          setVideoError(false);
          await trackCustomEvent('video_url_validation_success', {
            category: 'Video',
            response_time: responseTime,
            retry_count: retryCount
          });
        }
      } catch (error) {
        const responseTime = Date.now() - testStartTime;

        // Handle network errors or CORS issues
        if (!navigator.onLine) {
          setVideoError('network');
          await trackError('video_url_offline', 'User offline during validation', 'ResultsScreen', {
            retry_count: retryCount
          });
        } else if (error instanceof Error && error.message === 'Request timeout') {
          setVideoError('timeout');
          await trackError('video_url_timeout', userData.videoUrl, 'ResultsScreen', {
            response_time: responseTime,
            retry_count: retryCount
          });
        } else if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
          // CORS error or network issue
          setVideoError('cors_error');
          await trackError('video_url_cors_error', userData.videoUrl, 'ResultsScreen', {
            response_time: responseTime,
            retry_count: retryCount,
            error_message: error.message
          });
        } else {
          setVideoError('unknown_error');
          await trackError('video_url_unknown_error', `${error}`, 'ResultsScreen', {
            response_time: responseTime,
            retry_count: retryCount,
            error_type: error?.constructor?.name
          });
        }
        setVideoLoading(false);
      }
    };

    testVideoUrl();
  }, [userData?.videoUrl, videoUrlTested, retryCount]);

  const handleShare = async () => {
    try {

      // Use the mp4 URL for sharing if available, otherwise fall back to video URL or placeholder
      const shareUrl = userData?.mp4Url || userData?.videoUrl || "https://www.w3schools.com/html/mov_bbb.mp4";

      const data = await openShareSheet({
        type: "video",
        data: {
          videoThumb: "https://th.bing.com/th/id/OIP.diLjyDJykb0UdbKGRrtEVQAAAA?o=7rm=3&rs=1&pid=ImgDetMain&o=7&rm=3",
          videoUrl: shareUrl,
          width: 480,
          height: 270,
        },
      });
      console.log(data);

      // Track successful share
      const sessionId = localStorage.getItem('gameState') ?
        JSON.parse(localStorage.getItem('gameState') || '{}').sessionId :
        'unknown';
      await trackShare(sessionId, 'link', 'zalo_share_sheet');
    } catch (error) {
      console.error('Error sharing video:', error);
    }
  };

  const handleShareButtonClick = () => {
    if (platform === 'ios') {
      // For iOS: show download dialog instead of share
      setShowDownloadDialog(true);
    } else {
      // For Android and others: call share function directly
      handleShare();
    }
  };

  const downloadVideo = async () => {
    // Set loading state to true at start and reset progress
    setIsDownloading(true);
    setDownloadProgress(0);

    try {
      // Track download video event
      await sendGA4Event({
        name: 'download_video_clicked',
        params: {
          screen_name: 'ResultsScreen',
          button_location: 'direct_download',
          button_text: 'TẢI VỀ'
        }
      });

      // Use saveVideoToGallery with mp4Url as primary source and progress callback
      await saveVideoToGallery({
        videoUrl: userData?.mp4Url || userData?.videoUrl || "",
        onProgress: (progress) => {
          setDownloadProgress(progress);
          console.log(`Đã lưu được ${progress}%...`);
        }
      });

      // Show success message
      openSnackbar({
        text: "Video đã được lưu vào thư viện!",
        type: "success",
        duration: 3000
      });
    } catch (error) {
      console.error(error);
      // Show error message
      openSnackbar({
        text: "Không thể lưu video. Vui lòng thử lại!",
        type: "error",
        duration: 3000
      });
    } finally {
      // Always set loading state to false and reset progress when completed
      setIsDownloading(false);
      setDownloadProgress(0);
    }
  };

  const handleVideoLoad = () => {
    setVideoLoading(false);
    setVideoError(false);
  };

  const handleVideoError = () => {
    setVideoLoading(false);

    // Determine error type based on network status and URL validity
    if (!networkStatus) {
      setVideoError('network');
    } else if (userData?.videoUrl && !isValidUrl(userData.videoUrl)) {
      setVideoError('invalid_url');
    } else if (retryCount >= 3) {
      setVideoError('max_retries');
    } else {
      setVideoError('server_error');
    }
  };

  const retryVideoLoad = async () => {
    // Implement rate limiting - minimum 2 seconds between retries
    const now = Date.now();
    const timeSinceLastRetry = now - lastRetryTime;
    const minRetryInterval = 2000; // 2 seconds

    if (timeSinceLastRetry < minRetryInterval) {
      // Track rate limit violation
      await trackCustomEvent('video_retry_rate_limited', {
        category: 'Video',
        time_since_last_retry: timeSinceLastRetry,
        retry_count: retryCount
      });
      return;
    }

    // Check retry limit
    const maxRetries = 5;
    if (retryCount >= maxRetries) {
      setVideoError('max_retries');
      await trackError('video_max_retries_reached', userData?.videoUrl || 'unknown', 'ResultsScreen', {
        retry_count: retryCount,
        total_duration: Date.now() - validationStartTime
      });
      return;
    }

    if (!networkStatus) {
      // Don't retry if offline
      await trackCustomEvent('video_retry_blocked_offline', {
        category: 'Video',
        retry_count: retryCount
      });
      return;
    }

    // Track retry video load event
    await sendGA4Event({
      name: 'retry_video_load_clicked',
      params: {
        screen_name: 'ResultsScreen',
        retry_count: retryCount,
        error_type: videoError || 'unknown',
        time_since_error: now - lastRetryTime
      }
    });

    setLastRetryTime(now);
    setVideoError(false);
    setVideoLoading(true);
    setRetryCount(retryCount + 1);
    setVideoUrlTested(false); // Reset to allow re-testing the URL
  };

  const isValidUrl = (url: string) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const getErrorContent = (errorType: string) => {
    switch (errorType) {
      case 'network':
        return {
          icon: '📡',
          title: 'Mất kết nối mạng',
          message: 'Vui lòng kiểm tra kết nối internet của bạn và thử lại.',
          showRetry: true,
          showDownloadHint: false,
          actionText: 'Kiểm tra kết nối'
        };
      case 'invalid_url':
        return {
          icon: '🔗',
          title: 'Đường dẫn không hợp lệ',
          message: 'Video của bạn có đường dẫn không đúng. Vui lòng liên hệ bộ phận hỗ trợ.',
          showRetry: false,
          showDownloadHint: false,
          supportInfo: 'Hotline: 1900-xxxx'
        };
      case 'max_retries':
        return {
          icon: '😔',
          title: 'Không thể tải video',
          message: 'Đã thử nhiều lần nhưng không thể tải video. Bạn vẫn có thể tải về máy.',
          showRetry: false,
          showDownloadHint: true
        };
      case 'server_error':
        return {
          icon: '⚙️',
          title: 'Lỗi tạm thời',
          message: 'Hệ thống đang gặp sự cố. Vui lòng thử lại sau ít phút.',
          showRetry: true,
          showDownloadHint: retryCount > 1,
          actionText: 'Thử lại'
        };
      case 'processing':
        return {
          icon: '🎬',
          title: 'Video đang được xử lý',
          message: 'Video của bạn đang trong quá trình xử lý. Vui lòng đợi 1-2 phút.',
          showRetry: true,
          showDownloadHint: false,
          actionText: 'Làm mới'
        };
      case 'not_found':
        return {
          icon: '🔍',
          title: 'Không tìm thấy video',
          message: 'Video của bạn chưa sẵn sàng hoặc đã bị xóa. Vui lòng thử lại sau.',
          showRetry: true,
          showDownloadHint: false,
          actionText: 'Kiểm tra lại'
        };
      case 'access_denied':
        return {
          icon: '🔒',
          title: 'Không có quyền truy cập',
          message: 'Bạn không có quyền xem video này. Vui lòng liên hệ hỗ trợ nếu đây là lỗi.',
          showRetry: false,
          showDownloadHint: false,
          supportInfo: 'Hotline: 1900-xxxx'
        };
      case 'cors_error':
        return {
          icon: '🌐',
          title: 'Lỗi kết nối',
          message: 'Không thể kết nối đến máy chủ video. Vui lòng thử lại sau.',
          showRetry: true,
          showDownloadHint: true,
          actionText: 'Thử lại'
        };
      case 'unknown_error':
        return {
          icon: '❓',
          title: 'Lỗi không xác định',
          message: 'Đã xảy ra lỗi không mong muốn. Vui lòng thử tải video về máy.',
          showRetry: true,
          showDownloadHint: true,
          actionText: 'Thử lại'
        };
      case 'timeout':
        return {
          icon: '⏱️',
          title: 'Quá thời gian chờ',
          message: 'Máy chủ phản hồi quá lâu. Vui lòng thử lại sau.',
          showRetry: true,
          showDownloadHint: retryCount > 2,
          actionText: 'Thử lại'
        };
      case 'rate_limited':
        return {
          icon: '🚦',
          title: 'Vượt giới hạn truy cập',
          message: 'Bạn đã thử quá nhiều lần. Vui lòng đợi một lát rồi thử lại.',
          showRetry: false,
          showDownloadHint: true,
          waitTime: '30 giây'
        };
      default:
        return {
          icon: '❌',
          title: 'Có lỗi xảy ra',
          message: 'Không thể hiển thị video. Vui lòng thử lại sau.',
          showRetry: true,
          showDownloadHint: true,
          actionText: 'Thử lại'
        };
    }
  };

  return (
    <Page className="results-screen">
      {/* Header */}
      <GameHeader />

      {/* Main Content Area */}
      <Box className="main-content !p-0 results-main-content" style={{ position: 'relative' }}>
        {/* Hero Section */}
        <img
          src={flowerImageDecor}
          alt="Flower"
          style={{ position: 'absolute', top: '-16px', left: 0 }}
          className="h-32"
        />
        <Box className="hero-section !p-[20px] !m-0 !pb-0">

          {/* Video Section - 9:16 aspect ratio */}
          <Box className="video-section video-wrapper">
            {userData?.videoUrl && isValidUrl(userData.videoUrl) ? (
              videoError ? (
                <Box className="video-error-container">
                  <Box className="error-content">
                    {(() => {
                      const errorContent = getErrorContent(videoError);
                      return (
                        <>
                          <Text className="error-icon">{errorContent.icon}</Text>
                          <Text className="error-title gradient-text-gold">
                            {errorContent.title}
                          </Text>
                          <Text className="error-message">
                            {errorContent.message}
                          </Text>

                          {errorContent.supportInfo && (
                            <Text className="support-info">
                              {errorContent.supportInfo}
                            </Text>
                          )}

                          {errorContent.waitTime && (
                            <Text className="wait-time-info">
                              Vui lòng đợi {errorContent.waitTime} trước khi thử lại
                            </Text>
                          )}

                          {errorContent.showRetry && (
                            <button
                              className="retry-button"
                              onClick={retryVideoLoad}
                              disabled={!networkStatus && videoError === 'network'}
                            >
                              <Text className="retry-text">{errorContent.actionText || 'Thử lại'}</Text>
                            </button>
                          )}

                          {errorContent.showDownloadHint && (
                            <Box className="error-actions">
                              <Text className="error-suggestion">
                                Bạn vẫn có thể tải video về máy
                              </Text>
                              <button
                                className="download-hint-button"
                                onClick={() => setShowDownloadDialog(true)}
                              >
                                <Text className="download-hint-text">Mở hộp thoại tải về</Text>
                              </button>
                            </Box>
                          )}

                          {!networkStatus && (
                            <Box className="network-status-indicator">
                              <Text className="offline-badge">Đang offline</Text>
                            </Box>
                          )}
                        </>
                      );
                    })()}
                  </Box>
                </Box>
              ) : (
                <Box className="video-container">
                  {videoLoading && (
                    <Box className="video-loading-overlay">
                      <Box className="loading-spinner" />
                      <Text className="loading-text">Đang tải video...</Text>
                    </Box>
                  )}
                  <iframe
                    key={retryCount}
                    src={userData.videoUrl}
                    className="video-iframe"
                    title="Personalized Video Result"
                    allow="autoplay; fullscreen"
                    allowFullScreen
                    onLoad={handleVideoLoad}
                    onError={handleVideoError}
                  />
                </Box>
              )
            ) : (
              <Box className="video-error-container">
                <Box className="error-content">
                  {(() => {
                    const errorContent = !userData?.videoUrl
                      ? getErrorContent('processing')
                      : getErrorContent('invalid_url');
                    return (
                      <>
                        <Text className="error-icon">{errorContent.icon}</Text>
                        <Text className="error-title gradient-text-gold">
                          {errorContent.title}
                        </Text>
                        <Text className="error-message">
                          {errorContent.message}
                        </Text>

                        {errorContent.supportInfo && (
                          <Text className="support-info">
                            {errorContent.supportInfo}
                          </Text>
                        )}

                        {!userData?.videoUrl && (
                          <Box className="processing-indicator">
                            <Box className="processing-bar">
                              <Box className="processing-progress" />
                            </Box>
                            <Text className="processing-text">Đang xử lý...</Text>
                          </Box>
                        )}
                      </>
                    );
                  })()}
                </Box>
              </Box>
            )}
          </Box>

          {/* Text Content Section */}
          <Box className="text-content">
            <Text className="gradient-text share-heading">
              Khoe ngay với Cha Mẹ <br />
              Phiếu  Bé Ngoan Cường
            </Text>

            <Text className="gradient-text share-subheading">
              và chia sẻ để có cơ hội nhận quà tặng hấp dẫn
            </Text>
          </Box>
        </Box>
        <img
          src={prizesImage}
          className='w-full z-9'
          alt="Prize"
        />
      </Box>

      {/* Footer Section */}
      <Box className="selected-zone">
        <Box className="action-buttons">
          <Box className="navigation-buttons !mb-[-12px]">
            <button className="image-button" onClick={downloadVideo} disabled={isDownloading}>
              <img src={selectButtonImage2} alt="Tải về" className='!w-full' />
              <span className="button-text !pb-1 gradient-text">
                {isDownloading ? (downloadProgress > 0 ? `${downloadProgress}%` : 'ĐANG TẢI...') : 'TẢI VỀ'}
              </span>
            </button>

            <button className="image-button" onClick={handleShareButtonClick}>
              <img src={selectButtonImage2} alt="Chia sẻ" className='!w-full' />
              <span className="button-text !pb-1 gradient-text">CHIA SẺ</span>
            </button>
          </Box>
        </Box>
        {/* Brand Logo */}
        <img 
          src={logoBrandImage} 
          alt="Brand Logo" 
          style={{
            position: 'absolute',
            bottom: '18px',
            right: '14px',
            width: 'auto',
            height: '16px',
            zIndex: 10
          }}
        />

        {/* QC */}
        <div 
        style={{
            fontSize: '6px',
            color: 'white',
            position: 'absolute',
            bottom: '14px',
            left: '14px',
            width: 'auto',
            height: '20px',
            zIndex: 10
          }}>
          ENS-C-630-25
        </div>
      </Box>

      {/* Download Dialog Modal */}
      <Modal
        visible={showDownloadDialog}
        onClose={() => setShowDownloadDialog(false)}
        className="download-dialog-modal"
      >
        <Box className="download-dialog">
          <button
            className="close-button"
            onClick={() => setShowDownloadDialog(false)}
            aria-label="Đóng"
            role="button"
          >
            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M13 1L1.00081 12.9992M12.9992 13L1 1.00085" stroke="url(#paint0_linear_395_3690)" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              <defs>
                <linearGradient id="paint0_linear_395_3690" x1="7" y1="1" x2="7" y2="13" gradientUnits="userSpaceOnUse">
                  <stop stopColor="#1A3A6F" />
                  <stop offset="0.5" stopColor="#2071B3" />
                  <stop offset="1" stopColor="#1A3A6F" />
                </linearGradient>
              </defs>
            </svg>
          </button>
          <Box className="download-dialog-content">
            <Text className="download-heading gradient-text-gold">
              Video đã sẵn sàng
            </Text>
            <Text className="download-subtitle gradient-text-gold">
              Tải về máy để chia sẻ ngay thôi
            </Text>

            <Box className="instructions-section">
              <Text className="instructions-header">
                HƯỚNG DẪN CHIA SẺ VIDEO
              </Text>
              <Box className="instructions-content">
                <Text className="instructions-text">
                  Bước 1: Bấm chọn “Tải về ngay” để tải video của bạn
                </Text>
                <Text className="instructions-text">
                  Bước 2: Chia sẻ video lên các nền tảng mạng xã hội (Facebook, Tiktok...) để tham gia chương trình và có cơ hội nhận quà nhé!
                </Text>
              </Box>
            </Box>
            <Box className="action-buttons">
              <Box className="navigation-buttons">
                <button className="image-button" onClick={downloadVideo} disabled={isDownloading}>
                  <img src={selectButtonImage} alt="tải về" className='max-w-[80%] m-auto' />
                  <span className="button-text gradient-text">
                    {isDownloading ? (downloadProgress > 0 ? `${downloadProgress}%` : 'ĐANG TẢI...') : 'TẢI VỀ NGAY'}
                  </span>
                </button>
              </Box>
            </Box>
          </Box>
        </Box>
      </Modal>
    </Page>
  );
};

export default ResultsScreen;