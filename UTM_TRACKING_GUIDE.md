# UTM Parameter Tracking Implementation Guide

This guide explains how UTM parameters are now integrated into your Zalo Mini Program's Google Analytics 4 tracking.

## Overview

UTM parameters are automatically extracted from URLs and included in all GA4 events. This works in both regular web browsers and Zalo Mini Program environments.

## UTM Parameters Tracked

- **utm_source**: The referrer (e.g., google, newsletter, zalo)
- **utm_medium**: Marketing medium (e.g., cpc, banner, email)
- **utm_campaign**: Campaign name (e.g., spring_sale)
- **utm_term**: Search terms (for paid search)
- **utm_content**: Content/creative variant for A/B testing

## How It Works

### 1. Automatic Extraction on App Load

When the app initializes, UTM parameters are automatically extracted from:
- URL query parameters (`?utm_source=...`)
- URL hash parameters (`#utm_source=...`)
- Zalo Mini Program parameters (via `zalo.getQueryParams()`)

### 2. Persistent Storage

UTM parameters are stored in localStorage for 24 hours, ensuring they're available throughout the user session even after navigation.

### 3. Automatic Inclusion in Events

All GA4 events automatically include UTM parameters mapped to GA4's campaign fields:
- `utm_source` → `campaign_source`
- `utm_medium` → `campaign_medium`
- `utm_campaign` → `campaign_name`
- `utm_term` → `campaign_term`
- `utm_content` → `campaign_content`

## Example URLs

### Regular Web
```
https://yourapp.com/?utm_source=facebook&utm_medium=social&utm_campaign=summer2024
```

### Zalo Mini Program
```
zalo://app/1234567890?utm_source=zalo_chat&utm_medium=message&utm_campaign=friend_share
```

## Testing UTM Tracking

### 1. Test in Browser Console

```javascript
// Run the UTM test suite
testUTM();
```

### 2. Manual Testing

1. Open your app with UTM parameters:
   ```
   http://localhost:3000/?utm_source=test&utm_medium=test&utm_campaign=test_campaign
   ```

2. Open browser DevTools and check the Network tab for GA4 requests

3. Look for these parameters in the events:
   - `campaign_source`
   - `campaign_medium`
   - `campaign_name`

### 3. Check GA4 Reports

In Google Analytics 4:
1. Go to Reports → Acquisition → Traffic acquisition
2. You should see your UTM parameters in the campaign dimensions

## Implementation Details

### Files Modified

1. **`src/utils/utm-tracking.ts`** (NEW)
   - Core UTM extraction and storage logic
   - Handles both web and Zalo environments
   - Provides persistent storage with 24-hour expiry

2. **`src/utils/ga4-tracking.ts`**
   - Updated to import UTM utilities
   - Modified `sendGA4Event` to include UTM parameters
   - Added `trackUTMParameters` event for debugging

3. **`src/types/ga4.d.ts`**
   - Added TypeScript types for Zalo SDK methods

### Key Functions

```typescript
// Extract UTM parameters from current URL
const params = extractUTMParameters();

// Get stored or current UTM parameters
const currentParams = getCurrentUTMParameters();

// Initialize UTM tracking (called automatically)
const utmParams = initUTMTracking();
```

## Zalo Mini Program Considerations

The implementation handles Zalo-specific URL parameter access:

1. **Primary Method**: Uses `window.zalo.getQueryParams()` if available
2. **Fallback**: Standard URL parsing works in Zalo webview
3. **Deep Link Support**: Handles encoded parameters in Zalo deep links

## Best Practices

1. **Consistent Naming**: Use lowercase, underscore-separated UTM parameters
2. **Campaign Structure**: Plan your campaign naming convention
3. **Testing**: Always test UTM tracking before launching campaigns
4. **Documentation**: Document your UTM parameter conventions for your team

## Troubleshooting

### UTM Parameters Not Showing

1. Check browser console for errors
2. Verify parameters are in the URL
3. Check localStorage: `localStorage.getItem('ga4_utm_params')`
4. Enable debug mode by checking console logs

### Zalo Mini Program Issues

1. Ensure Zalo SDK is loaded
2. Check if parameters are passed correctly in deep links
3. Test both direct URLs and shared links

## Example Campaign URLs

### Facebook Campaign
```
https://yourapp.com/?utm_source=facebook&utm_medium=paid_social&utm_campaign=summer_2024&utm_content=video_ad_1
```

### Email Newsletter
```
https://yourapp.com/?utm_source=newsletter&utm_medium=email&utm_campaign=weekly_digest&utm_content=header_cta
```

### Zalo Message Share
```
zalo://app/1234567890?utm_source=zalo&utm_medium=message&utm_campaign=friend_referral&utm_content=share_button
```

## Verification in GA4

To verify UTM tracking is working:

1. Real-time Report: Check Realtime → Event count by Event name
2. Look for `utm_parameters_captured` event
3. Check event parameters for campaign fields
4. Wait 24-48 hours for full data in acquisition reports