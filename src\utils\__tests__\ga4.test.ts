// GA4 Integration Tests
import { ga4, trackEvent, isZaloMiniApp, getClientId, getSessionId } from '../ga4';
import { sendGA4Event, trackScreenView, trackGameStart } from '../ga4-tracking';

describe('GA4 Integration Tests', () => {
  // Mock localStorage
  const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    clear: jest.fn()
  };
  
  beforeEach(() => {
    // Reset mocks
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    localStorageMock.clear.mockClear();
    
    // Set up localStorage mock
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      writable: true
    });
    
    // Reset window location
    delete (window as any).location;
    (window as any).location = {
      protocol: 'https:',
      hostname: 'localhost',
      href: 'https://localhost',
      pathname: '/'
    };
  });
  
  describe('Helper Functions', () => {
    test('isZaloMiniApp detects zbrowser protocol', () => {
      (window as any).location.protocol = 'zbrowser:';
      expect(isZaloMiniApp()).toBe(true);
      
      (window as any).location.protocol = 'https:';
      expect(isZaloMiniApp()).toBe(false);
    });
    
    test('getClientId generates and persists client ID', () => {
      localStorageMock.getItem.mockReturnValue(null);
      
      const clientId1 = getClientId();
      expect(clientId1).toBeTruthy();
      expect(localStorageMock.setItem).toHaveBeenCalledWith('ga4_client_id', clientId1);
      
      // Should return same ID on subsequent calls
      localStorageMock.getItem.mockReturnValue(clientId1);
      const clientId2 = getClientId();
      expect(clientId2).toBe(clientId1);
    });
    
    test('getSessionId generates and expires session', () => {
      const now = Date.now();
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'ga4_session_expiry') return null;
        return null;
      });
      
      const sessionId1 = getSessionId();
      expect(sessionId1).toBeTruthy();
      expect(localStorageMock.setItem).toHaveBeenCalledWith('ga4_session_id', sessionId1);
      
      // Should generate new session if expired
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'ga4_session_expiry') return (now - 40 * 60 * 1000).toString(); // Expired
        if (key === 'ga4_session_id') return sessionId1;
        return null;
      });
      
      const sessionId2 = getSessionId();
      expect(sessionId2).not.toBe(sessionId1);
    });
  });
  
  describe('Event Tracking', () => {
    test('trackEvent adds enhanced parameters', () => {
      const mockTrackEvent = jest.spyOn(ga4, 'trackEvent');
      
      trackEvent('test_event', { custom_param: 'test' });
      
      expect(mockTrackEvent).toHaveBeenCalledWith('test_event', 
        expect.objectContaining({
          session_id: expect.any(String),
          engagement_time_msec: 100,
          platform: 'web',
          custom_param: 'test'
        })
      );
      
      mockTrackEvent.mockRestore();
    });
    
    test('sendGA4Event includes UTM parameters', async () => {
      const mockTrackEvent = jest.spyOn(ga4, 'trackEvent');
      
      await sendGA4Event({
        name: 'test_event',
        params: { test_param: 'value' }
      });
      
      expect(mockTrackEvent).toHaveBeenCalled();
      mockTrackEvent.mockRestore();
    });
    
    test('trackScreenView sends correct parameters', async () => {
      const mockTrackEvent = jest.spyOn(ga4, 'trackEvent');
      
      await trackScreenView('TestScreen', 'TestClass');
      
      expect(mockTrackEvent).toHaveBeenCalledWith('page_view', 
        expect.objectContaining({
          page_title: 'TestScreen',
          screen_name: 'TestScreen',
          screen_class: 'TestClass'
        })
      );
      
      mockTrackEvent.mockRestore();
    });
    
    test('trackGameStart includes session ID', async () => {
      const mockTrackEvent = jest.spyOn(ga4, 'trackEvent');
      
      await trackGameStart('test-session-123');
      
      expect(mockTrackEvent).toHaveBeenCalledWith('game_start', 
        expect.objectContaining({
          game_session_id: 'test-session-123',
          timestamp: expect.any(String)
        })
      );
      
      mockTrackEvent.mockRestore();
    });
  });
  
  describe('Environment Detection', () => {
    test('uses Measurement Protocol for zbrowser', () => {
      (window as any).location.protocol = 'zbrowser:';
      
      // The useMeasurementProtocolWhen function should return true
      const config = (ga4 as any).config;
      if (config && config.useMeasurementProtocolWhen) {
        expect(config.useMeasurementProtocolWhen()).toBe(true);
      }
    });
    
    test('uses gtag.js for https', () => {
      (window as any).location.protocol = 'https:';
      
      // The useMeasurementProtocolWhen function should return false
      const config = (ga4 as any).config;
      if (config && config.useMeasurementProtocolWhen) {
        expect(config.useMeasurementProtocolWhen()).toBe(false);
      }
    });
  });
});