# GA4 Integration Upgrade with zmp-ga4

## Overview

The GA4 integration has been successfully upgraded to use the `zmp-ga4` package (v2.2.2), which provides adaptive tracking that automatically selects between gtag.js and Measurement Protocol based on the environment.

## Key Changes

### 1. New Dependencies
- **zmp-ga4@2.2.2**: Adaptive GA4 tracking library that handles both gtag.js and Measurement Protocol

### 2. New Files Created
- `/src/utils/ga4.ts`: Core GA4 module using AdaptiveAnalytics
- `/src/types/window.d.ts`: TypeScript definitions for window extensions
- `/src/utils/__tests__/ga4.test.ts`: Test suite for GA4 integration

### 3. Modified Files
- `/src/utils/ga4-tracking.ts`: Updated to use zmp-ga4 instead of manual implementation

## Features

### Adaptive Tracking
The integration automatically selects the appropriate tracking method:
- **gtag.js**: Used for standard web environments (http/https protocols)
- **Measurement Protocol**: Used for Zalo Mini App environment (zbrowser:// protocol) and iOS devices

### Configuration
```typescript
// GA4 Configuration in /src/utils/ga4.ts
const MEASUREMENT_ID = "G-SSFXY6G7HT";
const API_SECRET = "4414522757";

export const ga4 = new AdaptiveAnalytics(
  MEASUREMENT_ID,
  API_SECRET,
  {
    useMeasurementProtocolWhen: () => {
      const isZbrowser = window.location.protocol === "zbrowser:";
      const isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);
      return isZbrowser || (isZbrowser && isIOS);
    },
    gtagConfig: {
      send_page_view: false, // Manual page view tracking
    },
  }
);
```

### Event Tracking
All existing events are preserved with the same names and parameters:
- `page_view`
- `game_start`
- `image_upload`
- `survey_response`
- `survey_complete`
- `game_complete`
- `share`
- `game_restart`
- `error`
- Custom events

### Enhanced Parameters
Every event automatically includes:
- `session_id`: 30-minute session tracking
- `engagement_time_msec`: Engagement time metric
- `platform`: 'zalo_mini_app' or 'web'
- UTM parameters (when available)

## Usage Example

```typescript
import { trackScreenView, trackGameStart, sendGA4Event } from '@/utils/ga4-tracking';

// Track screen view
await trackScreenView('WelcomeScreen', 'Game');

// Track game start
await trackGameStart('session-123');

// Track custom event
await sendGA4Event({
  name: 'custom_event',
  params: {
    custom_param: 'value'
  }
});
```

## Testing

Run the test suite to verify the integration:
```bash
npm test src/utils/__tests__/ga4.test.ts
```

## Debugging

In development mode (localhost), all events are logged to the console with:
- Event name and parameters
- Transport method (gtag vs measurement_protocol)
- Environment detection

## Benefits

1. **Automatic Protocol Selection**: No manual configuration needed for different environments
2. **CORS Compliance**: Measurement Protocol bypasses CORS restrictions in Zalo Mini App
3. **Unified API**: Same tracking code works across all platforms
4. **Type Safety**: Full TypeScript support with proper typing
5. **Backwards Compatible**: All existing event tracking calls continue to work

## Monitoring

Use GA4 DebugView to monitor events:
1. Enable debug mode in GA4
2. Check real-time event flow
3. Verify correct transport method based on environment

## Edge Cases Handled

- Missing GA4 credentials → Graceful error handling
- Offline mode → Events queued (if supported by zmp-ga4)
- Invalid parameters → Sanitized before sending
- Multiple environments → Automatic detection and adaptation