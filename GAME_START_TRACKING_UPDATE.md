# Game Start Event Tracking Update

## Overview

The `trackGameStart` function has been updated to include additional contextual parameters for better analytics in the Zalo Mini Program. The function now captures more detailed information about the game start event while maintaining backward compatibility.

## Updated Function Signature

```typescript
export const trackGameStart = async (
  sessionId: string,
  additionalParams?: {
    user_id?: string;
    platform?: 'zalo_mini_app' | 'web';
    utm_source?: string;
    utm_medium?: string;
    utm_campaign?: string;
    timestamp?: string;
    screen_name?: string;
  }
): Promise<void>
```

## Parameters Included in game_start Event

1. **game_session_id** (required): Unique identifier for the game session
2. **user_id** (optional): User identifier if available
3. **platform**: Automatically detected as 'zalo_mini_app' or 'web'
4. **utm_source**: Campaign source (auto-filled from URL/storage)
5. **utm_medium**: Campaign medium (auto-filled from URL/storage)
6. **utm_campaign**: Campaign name (auto-filled from URL/storage)
7. **timestamp**: ISO string of when the game started
8. **screen_name**: The screen where game start was triggered

## Implementation Details

### Automatic Parameter Detection

- **Platform**: Automatically detects if running in Zalo Mini Program or web browser
- **UTM Parameters**: Automatically retrieves from current session if not explicitly provided
- **Timestamp**: Generates ISO timestamp if not provided

### Usage Example

```typescript
// Basic usage (backward compatible)
await trackGameStart(sessionId);

// With additional parameters
await trackGameStart(sessionId, {
  screen_name: 'WelcomeScreen',
  user_id: 'zalo_user_123' // if available
});

// All parameters specified
await trackGameStart(sessionId, {
  user_id: 'zalo_user_123',
  platform: 'zalo_mini_app',
  utm_source: 'facebook',
  utm_medium: 'social',
  utm_campaign: 'summer_2024',
  timestamp: new Date().toISOString(),
  screen_name: 'WelcomeScreen'
});
```

## Current Implementation

In `WelcomeScreen.tsx`, the function is called with:
```typescript
await trackGameStart(sessionId, {
  screen_name: 'WelcomeScreen'
});
```

## Future Enhancements

### Getting Zalo User ID

To include the Zalo user ID in the game_start event, you could:

1. Import Zalo SDK functions:
```typescript
import { getUserInfo } from 'zmp-sdk';
```

2. Get user info before starting the game:
```typescript
const handleStartGame = async () => {
  let userId: string | undefined;
  
  // Try to get Zalo user ID if in Zalo Mini Program
  if (window.zalo) {
    try {
      const userInfo = await getUserInfo({});
      userId = userInfo.userInfo?.id;
    } catch (error) {
      console.log('Could not get Zalo user info:', error);
    }
  }
  
  await trackGameStart(sessionId, {
    screen_name: 'WelcomeScreen',
    user_id: userId
  });
};
```

## GA4 Event Structure

The `game_start` event will appear in GA4 with these parameters:

```json
{
  "name": "game_start",
  "params": {
    "game_session_id": "uuid-v4-string",
    "user_id": "zalo_user_id_if_available",
    "platform": "zalo_mini_app",
    "utm_source": "facebook",
    "utm_medium": "social",
    "utm_campaign": "summer_2024",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "screen_name": "WelcomeScreen",
    "campaign_source": "facebook",
    "campaign_medium": "social",
    "campaign_name": "summer_2024"
  }
}
```

Note: UTM parameters are included both as individual parameters and as campaign_* parameters for GA4 compatibility.

## Testing

1. **Test with UTM parameters**:
   ```
   https://yourapp.com/?utm_source=test&utm_medium=test&utm_campaign=test_game_start
   ```

2. **Verify in browser console**:
   - Check Network tab for GA4 requests
   - Look for `game_start` event
   - Verify all parameters are included

3. **Test in Zalo Mini Program**:
   - Share link with UTM parameters
   - Check if platform is correctly detected as 'zalo_mini_app'

## Benefits

1. **Better Attribution**: Track which campaigns drive game starts
2. **Platform Insights**: Understand user behavior across platforms
3. **User Journey**: Link game starts to specific users (when available)
4. **Screen Context**: Know where users initiated the game
5. **Timing Analysis**: Precise timestamps for funnel analysis