// Google Analytics 4 Tracking for Zalo Mini Program
// Using zmp-ga4 for adaptive tracking (gtag.js vs Measurement Protocol)

import { initUTMTracking, getCurrentUTMParameters, formatUTMForGA4 } from './utm-tracking';
import { ga4, trackEvent, getSessionId, isZaloMiniApp } from './ga4';

// GA4 Event Interface
export interface GA4Event {
  name: string;
  params?: Record<string, any>;
}

// Main event sending function using zmp-ga4
export const sendGA4Event = async (event: GA4Event): Promise<void> => {
  try {
    // Get current UTM parameters and format for GA4
    const utmParams = getCurrentUTMParameters();
    
    const formattedUTMParams = formatUTMForGA4(utmParams);
    
    // Prepare enhanced event params with UTM data
    const enhancedParams = {
      ...formattedUTMParams,
      ...event.params
    };
    
    // Use zmp-ga4 to track the event
    trackEvent(event.name, enhancedParams);
    
  } catch (error) {
    console.error('GA4 tracking error:', error);
    // Don't throw - tracking failures shouldn't break the app
  }
};

// Predefined event tracking functions
export const trackScreenView = async (screenName: string, screenClass?: string): Promise<void> => {
  await sendGA4Event({
    name: 'page_view',
    params: {
      page_title: screenName,
      page_location: window.location.href,
      page_path: window.location.pathname,
      screen_name: screenName,
      screen_class: screenClass || screenName
    }
  });
};

/**
 * @deprecated Use specific event tracking functions instead (e.g., trackGameStartClicked, trackShareClicked, etc.)
 * This function tracks generic button clicks which provides less meaningful analytics data.
 */
export const trackButtonClick = async (
  buttonName: string,
  screenName: string,
  additionalParams?: Record<string, any>
): Promise<void> => {
  await sendGA4Event({
    name: 'button_click',
    params: {
      button_name: buttonName,
      screen_name: screenName,
      ...additionalParams
    }
  });
};

export const trackGameStart = async (sessionId: string, additionalParams?: { screen_name?: string }): Promise<void> => {
  await sendGA4Event({
    name: 'game_start',
    params: {
      game_session_id: sessionId,
      timestamp: new Date().toISOString(),
      screen_name: additionalParams?.screen_name || 'WelcomeScreen',
      // user_id will be added if available from session or user context
      // platform, utm_source, utm_medium, utm_campaign are automatically detected
    }
  });
};

export const trackImageUpload = async (
  sessionId: string,
  imageCount: number,
  totalSize: number
): Promise<void> => {
  await sendGA4Event({
    name: 'image_upload',
    params: {
      game_session_id: sessionId,
      image_count: imageCount,
      total_size_bytes: totalSize,
      screen_name: 'ImageUploadScreen'
    }
  });
};

export const trackSurveyResponse = async (
  sessionId: string,
  questionId: string,
  questionText: string,
  answerType: string
): Promise<void> => {
  await sendGA4Event({
    name: 'survey_response',
    params: {
      game_session_id: sessionId,
      question_id: questionId,
      question_text: questionText,
      answer_type: answerType,
      screen_name: 'SurveyScreen'
    }
  });
};

export const trackSurveyComplete = async (
  sessionId: string,
  questionCount: number,
  completionTime: number
): Promise<void> => {
  await sendGA4Event({
    name: 'survey_complete',
    params: {
      game_session_id: sessionId,
      question_count: questionCount,
      completion_time_seconds: completionTime,
      screen_name: 'SurveyScreen'
    }
  });
};

export const trackGameComplete = async (
  sessionId: string,
  score: number,
  category: string,
  completionTime: number
): Promise<void> => {
  await sendGA4Event({
    name: 'game_complete',
    params: {
      game_session_id: sessionId,
      score: score,
      category: category,
      completion_time_seconds: completionTime,
      screen_name: 'ResultsScreen'
    }
  });
};

export const trackShare = async (
  sessionId: string,
  shareType: string,
  shareMethod: string
): Promise<void> => {
  await sendGA4Event({
    name: 'share',
    params: {
      game_session_id: sessionId,
      content_type: shareType,
      method: shareMethod,
      screen_name: 'ResultsScreen'
    }
  });
};

export const trackGameRestart = async (
  oldSessionId: string,
  newSessionId: string
): Promise<void> => {
  await sendGA4Event({
    name: 'game_restart',
    params: {
      old_session_id: oldSessionId,
      new_session_id: newSessionId,
      screen_name: 'ResultsScreen'
    }
  });
};

export const trackError = async (
  errorType: string,
  errorMessage: string,
  screenName: string,
  additionalParams?: Record<string, any>
): Promise<void> => {
  await sendGA4Event({
    name: 'error',
    params: {
      error_type: errorType,
      error_message: errorMessage,
      screen_name: screenName,
      ...additionalParams
    }
  });
};

// Custom event tracking for any other events
export const trackCustomEvent = async (
  eventName: string,
  params: Record<string, any>
): Promise<void> => {
  await sendGA4Event({
    name: eventName,
    params
  });
};

// Track UTM parameters as a separate event for campaign analysis
export const trackUTMParametersCaptured = async (): Promise<void> => {
  const utmParams = getCurrentUTMParameters();
  
  if (Object.keys(utmParams).length > 0) {
    await sendGA4Event({
      name: 'utm_parameters_captured',
      params: {
        capture_location: 'app_init',
        timestamp: new Date().toISOString(),
        ...utmParams
      }
    });
  }
};

// Specific action-based tracking functions
export const trackGameStartClicked = async (screenName: string, buttonLocation: string): Promise<void> => {
  const utmParams = getCurrentUTMParameters();
  await sendGA4Event({
    name: 'game_start_clicked',
    params: {
      screen_name: screenName,
      button_location: buttonLocation,
      ...utmParams
    }
  });
};

export const trackImageSelectClicked = async (currentImageCount: number): Promise<void> => {
  await sendGA4Event({
    name: 'image_select_clicked',
    params: {
      screen_name: 'ImageUploadScreen',
      button_location: 'upload_area',
      current_image_count: currentImageCount
    }
  });
};

export const trackImageUploadSubmitted = async (imageCount: number, userName: string): Promise<void> => {
  await sendGA4Event({
    name: 'image_upload_submitted',
    params: {
      screen_name: 'ImageUploadScreen',
      button_location: 'footer',
      image_count: imageCount,
      user_name: userName || 'not_provided'
    }
  });
};

export const trackSurveySubmitted = async (questionsAnswered: number): Promise<void> => {
  await sendGA4Event({
    name: 'survey_submitted',
    params: {
      screen_name: 'SurveyScreen',
      button_location: 'footer',
      questions_answered: questionsAnswered
    }
  });
};

export const trackShareResultsClicked = async (): Promise<void> => {
  await sendGA4Event({
    name: 'share_results_clicked',
    params: {
      screen_name: 'ResultsScreen',
      button_location: 'footer'
    }
  });
};

export const trackDownloadVideoClicked = async (location: 'dialog' | 'footer'): Promise<void> => {
  await sendGA4Event({
    name: 'download_video_clicked',
    params: {
      screen_name: 'ResultsScreen',
      button_location: location === 'dialog' ? 'download_dialog' : 'footer'
    }
  });
};

export const trackDownloadDialogOpened = async (): Promise<void> => {
  await sendGA4Event({
    name: 'download_dialog_opened',
    params: {
      screen_name: 'ResultsScreen',
      button_location: 'footer'
    }
  });
};

export const trackRetryVideoLoadClicked = async (retryCount: number, errorType: string): Promise<void> => {
  await sendGA4Event({
    name: 'retry_video_load_clicked',
    params: {
      screen_name: 'ResultsScreen',
      retry_count: retryCount,
      error_type: errorType || 'unknown'
    }
  });
};

// Initialize GA4 tracking
export const initGA4 = (): void => {
  // Initialize UTM tracking first
  const utmParams = initUTMTracking();
  
  console.log('Initializing GA4 tracking with zmp-ga4...', {
    isZaloMiniProgram: isZaloMiniApp(),
    measurementId: 'G-SSFXY6G7HT',
    transport: window.location.protocol === 'zbrowser:' ? 'measurement_protocol' : 'gtag',
    utmParameters: utmParams
  });
  
  // Small delay to ensure everything is loaded
  setTimeout(() => {
    // Track initial page view
    trackScreenView('App Launch', isZaloMiniApp() ? 'ZaloMiniProgram' : 'WebApp');
    
    // Track UTM parameters if present
    if (Object.keys(utmParams).length > 0) {
      trackUTMParametersCaptured();
    }
  }, 100);
  
  // Set up page visibility tracking
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
      trackCustomEvent('app_foreground', {
        timestamp: new Date().toISOString()
      });
    } else {
      trackCustomEvent('app_background', {
        timestamp: new Date().toISOString()
      });
    }
  });
  
  // Add global error tracking
  window.addEventListener('error', (event) => {
    trackError('javascript_error', event.message, 'global');
  });
  
  // Add unhandled promise rejection tracking
  window.addEventListener('unhandledrejection', (event) => {
    trackError('unhandled_promise_rejection', event.reason?.toString() || 'Unknown', 'global');
  });
};