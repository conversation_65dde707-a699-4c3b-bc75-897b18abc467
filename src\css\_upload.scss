// Image Upload Screen Styles
.image-upload-screen {
  input[type="file"] {
    display: none;
  }

  .selected-zone {
    max-width: 800px;
  }

  // Header Section with Title and Name Input
  .upload-header-section {
    margin-bottom: 28px;

    .upload-title {
      font-size: 20px;
      font-style: italic;
      font-weight: 700;
      line-height: normal;
      text-align: center;
      margin-bottom: 12px;
    }

    .name-input-label {
      color: #492405;
      text-align: center;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 16px;
      display: block;
      margin-bottom: 4px;
      background: transparent;
      border: none;
      outline: none;
      width: 100%;
      cursor: default;
    }

    .name-input-container {
      position: relative;
      text-align: center;

      .dotted-placeholder {
        color: #492405 !important;
        font-size: 16px;
        position: absolute;
        width: 100%;
        text-align: center;
        pointer-events: none;
        transition: opacity 0.3s ease;
      }

      
    }
  }

  .name-input {
    background: transparent;
    border: none;
    color: #492405;
    font-size: 16px;
    text-align: center;
    width: 100% !important;
    outline: none;
    padding: 4px 0;
    transition: all 0.3s ease;
    
    &::placeholder {
      color: #492405;
      font-size: 16px;
      text-align: center;
      font-weight: 400;
    }
    
    &::-webkit-input-placeholder {
      color: #492405;
      opacity: 0.85;
      font-weight: 400;
    }
    
    &::-moz-placeholder {
      color: #492405;
      opacity: 0.85;
      font-weight: 400;
    }
    
    &:-ms-input-placeholder {
      color: #492405;
      opacity: 0.85;
      font-weight: 400;
    }
    
    &:-moz-placeholder {
      color: #492405;
      opacity: 0.85;
      font-weight: 400;
    }
    
    // Validation states
    &.input-valid {
      color: #2a5d1f;
    }
    
    &.input-warning {
      color: #ff8800;
      font-weight: 500;
    }
  }

  // Photo Section with Frame and Upload
  .photo-upload-section {
    margin-top: 16px;
    text-align: center;
    position: relative;

    .upload-frame-container {
      cursor: pointer;
      display: inline-block;
      position: relative;

      .upload-frame {
        max-width: 100%;
        height: auto;
      }

      .uploaded-image-container {
        position: absolute;
        top: 50%;
        left: 49%;
        transform: translate(-50%, -49%) rotate(6.5deg) scale(0.85);
        width: 99%;
        height: 79%;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        border-radius: 4px; // Slight rounding to match photo aesthetic

        .uploaded-image {
          // Use contain to ensure the entire image is visible
          object-fit: contain;
          object-position: center;

          // For portrait images (taller than wide)
          &.portrait {
            width: auto;
            height: 100%;
            max-width: 100%;
          }

          // For landscape images (wider than tall)
          &.landscape {
            width: 100%;
            height: auto;
            max-height: 100%;
          }

          // Default before orientation is detected
          &:not(.portrait):not(.landscape) {
            max-width: 100%;
            max-height: 100%;
          }
        }
      }
    }
  }

  // Instructions Section
  .instructions-section {
    margin-top: 0px;

    .instructions-text {
      text-align: center;
      font-size: 20px;
      font-style: italic;
      font-weight: 700;
      line-height: 24px;
      margin: 0;
    }
  }

  // Image Orientation Error Modal Styles
  .image-orientation-error-modal {
    .zaui-modal-content {
      background: transparent !important;
      padding: 0 !important;
      max-width: 90vw;
      width: 100%;
      max-width: 400px;
    }

    .orientation-error-dialog {
      background: radial-gradient(50% 219.26% at 50% 50%, #133985 0%, #023C81 71.63%, #1C2664 100%);
      border-radius: 16px;
      padding: 24px;
      text-align: center;
      position: relative;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);

      .close-button {
        position: absolute;
        top: 0;
        right: 0;
        transform: translate(50%, -50%);
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: linear-gradient(180deg, #DCAD5D 0%, #DED2A4 25%, #D8BC66 76.44%, #DCAD5D 100%);
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
        transition: all 0.2s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        
        &:hover {
          transform: translate(50%, -50%) scale(1.1);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        &:active {
          transform: translate(50%, -50%) scale(0.95);
        }
        
        svg {
          width: 14px;
          height: 14px;
        }
      }

      .error-dialog-content {
        display: flex;
        flex-direction: column;
        align-items: center;

        .error-icon {
          font-size: 48px;
          margin-bottom: 16px;
          display: block;
        }

        .error-heading {
          font-size: 24px;
          font-weight: 900;
          font-style: italic;
          line-height: 32px;
          text-align: center;
          margin-bottom: 8px;
        }

        .error-subtitle {
          font-size: 14px;
          font-weight: 700;
          font-style: italic;
          line-height: 100%;
          margin-bottom: 24px;
          text-align: center;
        }

        .instructions-section {
          width: 100%;
          margin: 16px 0;
          
          .instructions-header {
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
            color: #DBAE5E;
            margin-bottom: 8px;
            text-align: left;
          }

          .instructions-content {
            color: #F4D893;
            border-radius: 8px;
            text-align: left;
          }

          .instructions-text {
            text-align: left;
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
            
            &:before {
              content: "•";
              position: absolute;
              left: 0;
              color: #DBB664;
              font-size: 18px;
              line-height: 20px;
            }
            
            &:last-child {
              margin-bottom: 0;
            }
          }
        }

        .action-buttons {
          display: flex;
          gap: 16px;
          margin-top: 24px;
          width: 100%;
          justify-content: center;

          .navigation-buttons {
            display: flex;
            justify-content: center;
            
            .image-button {
              background: transparent;
              border: none;
              cursor: pointer;
              transition: transform 0.2s ease;
              
              &:hover {
                transform: scale(1.05);
              }
              
              &:active {
                transform: scale(0.98);
              }
            }
          }
        }
      }
    }
    .image-button {
      margin-top: 16px;
      position: relative;

      .button-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        leading-trim: both;
        text-edge: cap;
        font-size: 18px;
        font-style: normal;
        font-weight: 900;
        line-height: 24px; /* 80% */
        pointer-events: none;
        white-space: nowrap;
        padding-bottom: 10px;
        padding-left: 40px;
      }
    }
  }

  // Responsive styles for smaller screens
  @media (max-width: 480px) {
    .image-orientation-error-modal {
      .orientation-error-dialog {
        padding: 20px;
        
        .close-button {
          width: 28px;
          height: 28px;
          
          svg {
            width: 12px;
            height: 12px;
          }
        }
        
        .error-dialog-content {
          .error-icon {
            font-size: 36px;
            margin-bottom: 12px;
          }
          
          .error-heading {
            font-size: 20px;
            line-height: 26px;
          }
          
          .error-subtitle {
            font-size: 12px;
          }
          
          .instructions-section {
            .instructions-header {
              font-size: 12px;
              line-height: 18px;
            }
            
            .instructions-text {
              font-size: 12px;
              line-height: 18px;
            }
          }
        }
      }
    }
  }

  @media (max-width: 360px) {
    .image-orientation-error-modal {
      .orientation-error-dialog {
        .close-button {
          width: 24px;
          height: 24px;
          
          svg {
            width: 10px;
            height: 10px;
          }
        }
      }
    }
  }
}