// TypeScript declarations for GA4 tracking

declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
    dataLayer?: any[];
    zalo?: {
      // Zalo Mini Program global object
      getQueryParams?: () => Record<string, string>;
      analytics?: {
        trackEvent: (eventName: string, params?: any) => void;
      };
      trackEvent?: (eventName: string, params?: any) => void;
    };
  }
}

export {};